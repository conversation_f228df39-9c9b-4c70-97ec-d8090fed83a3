# FocusFlyer

一个基于 Godot 引擎和 SwiftUI 的 iOS 应用，实现了 Godot 3D/2D 渲染作为底层，SwiftUI 作为悬浮控制层的混合架构。

## 项目特点

- **Godot 引擎底层渲染**: 使用 SwiftGodotKit 将 Godot 引擎嵌入 iOS 应用
- **SwiftUI 悬浮控制**: 在 Godot 渲染层之上提供原生 iOS 控件
- **Rive 动画集成**: 支持高质量的交互式 2D 动画
- **模块化架构**: 按功能分类组织代码，便于维护和扩展

## 技术栈

- **iOS**: Swift 5.9+, SwiftUI, iOS 17+
- **游戏引擎**: Godot 4.x + SwiftGodotKit
- **动画**: Rive + RiveRuntime
- **架构**: MVVM + ObservableObject

## 项目结构

```
focusflyer/
├── Resources/              # 所有资源文件
│   ├── GodotAssets/        # Godot 引擎资源 (.pck 文件)
│   ├── RiveAssets/         # Rive 动画资源 (.riv 文件)
│   └── Assets.xcassets/    # App 图标和颜色资源
├── Views/                  # SwiftUI 视图
│   ├── Main/               # 主视图 (App 入口、ContentView)
│   ├── Components/         # 可复用组件
│   ├── Godot/              # Godot 相关视图
│   └── Rive/               # Rive 相关视图
├── Managers/               # 管理器类 (引擎管理、状态管理)
└── Utils/                  # 工具类 (预留)
```

## 核心功能

### 1. Godot 引擎集成

- 使用 `GlobalGodotEngine` 管理器统一控制 Godot 引擎
- 支持 PCK 文件加载和场景切换
- 底层渲染始终运行，上层 SwiftUI 可以自由切换

### 2. SwiftUI 悬浮控制

- 左上角：`FloatingControlPanel` 场景控制面板
- 右下角：`RiveFloatingView` Rive 动画展示
- 中央：欢迎信息和状态显示

### 3. Rive 动画

- 支持交互式 2D 动画
- 可拖拽的悬浮动画按钮
- 展开式动画面板

## 开发指南

### 环境要求

- Xcode 15.0+
- iOS 17.0+
- Swift 5.9+

### 依赖库

- SwiftGodotKit
- RiveRuntime

### 添加新功能

#### 添加 Godot 场景

1. 在 Godot 编辑器中创建新场景
2. 导出为 iOS PCK 文件
3. 将 PCK 文件放入 `Resources/GodotAssets/`
4. 在 `PCKManager.swift` 中更新场景引用

#### 添加 Rive 动画

1. 将 .riv 文件放入 `Resources/RiveAssets/`
2. 在 SwiftUI 视图中使用 `RiveViewModel(fileName: "filename")`

#### 添加新的 SwiftUI 组件

1. 在 `Views/Components/` 目录创建新组件
2. 在 `ContentView.swift` 中集成新组件

## 架构设计

### 渲染层次

```
┌─────────────────────────────────────┐
│           SwiftUI 悬浮层              │  ← 控制面板、按钮、状态显示
├─────────────────────────────────────┤
│           Godot 渲染层               │  ← 3D/2D 游戏场景
└─────────────────────────────────────┘
```

### 数据流

- `GlobalGodotEngine`: 全局引擎状态管理
- `@StateObject`: SwiftUI 状态绑定
- `@Published`: 响应式数据更新

## 最佳实践

1. **单例模式**: 使用 `GlobalGodotEngine.shared` 确保引擎实例唯一性
2. **异步初始化**: 引擎初始化在后台线程进行，避免阻塞 UI
3. **状态管理**: 使用 enum 清晰定义引擎状态
4. **资源管理**: 按类型分类存放资源文件
5. **模块化**: 按功能拆分视图和管理器

## 构建和运行

1. 克隆仓库
2. 在 Xcode 中打开 `focusflyer.xcodeproj`
3. 确保已安装 SwiftGodotKit 和 RiveRuntime 依赖
4. 选择 iOS 设备或模拟器
5. 点击 Run 按钮

## 故障排除

### 常见问题

- **Godot 引擎启动失败**: 检查 PCK 文件是否正确放置在 Resources 目录
- **Rive 动画不显示**: 确认 .riv 文件已添加到 Xcode 项目中
- **控制面板不响应**: 检查 `GlobalGodotEngine` 的状态是否为 `.ready`

### 调试技巧

- 使用 `print()` 语句跟踪引擎初始化过程
- 在 FloatingControlPanel 中启用调试模式查看详细状态
- 检查 Xcode 控制台输出获取错误信息

## 许可证

[在此添加许可证信息]

## 贡献

欢迎提交 Issue 和 Pull Request。

## 更新日志

### v1.0.0

- 初始版本
- 集成 Godot 引擎和 SwiftUI
- 添加 Rive 动画支持
- 实现悬浮控制面板
