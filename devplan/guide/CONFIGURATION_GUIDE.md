# 📋 FocusFlyer 配置指南

## 🎯 快速配置

### 1. 编辑配置文件

打开 `focusflyer/Utils/BuildConfiguration.swift`，找到需要修改的配置项。

### 2. 常用配置修改

#### 🟢 推荐配置（当前默认）

```swift
// 智能渲染生命周期管理
static var enableSmartRenderingLifecycle: Bool {
  return true  // 🚨 重要：始终启用，节省电池
}

// 调试面板（仅Debug模式）
static var enableDebugPanel: Bool {
  #if DEBUG
    return true   // Debug模式显示
  #else
    return false  // Release模式隐藏
  #endif
}

// 性能监控（仅Debug模式）
static var enablePerformanceMonitoring: Bool {
  #if DEBUG
    return true   // Debug模式启用
  #else
    return false  // Release模式禁用
  #endif
}
```

#### 🔧 自定义配置示例

**场景1：Release版本也需要调试面板**

```swift
static var enableDebugPanel: Bool {
  #if DEBUG
    return true
  #else
    return true  // 📝 修改：Release版本也显示调试面板
  #endif
}
```

**场景2：Release版本需要轻量级性能监控**

```swift
static var enablePerformanceMonitoring: Bool {
  #if DEBUG
    return true
  #else
    return true  // 📝 修改：Release版本也启用性能监控
  #endif
}
```

**场景3：完全禁用智能渲染管理（不推荐）**

```swift
static var enableSmartRenderingLifecycle: Bool {
  return false  // ⚠️ 警告：会导致后台持续渲染，消耗电池
}
```

## 🔧 详细配置选项

### 核心功能配置

| 配置项 | 默认值 | 说明 | 推荐设置 |
|--------|--------|------|----------|
| `enableSmartRenderingLifecycle` | `true` | 后台渲染控制 | ✅ 始终启用 |
| `enableDebugPanel` | Debug: `true`<br/>Release: `false` | 调试面板显示 | 按需调整 |
| `enablePerformanceMonitoring` | Debug: `true`<br/>Release: `false` | 性能监控 | 按需调整 |
| `enableVerboseLogging` | Debug: `true`<br/>Release: `false` | 详细日志 | 按需调整 |
| `enableTripleTapDebug` | Debug: `true`<br/>Release: `false` | 三击手势调试 | 按需调整 |

### 高级配置

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `showKnownIssuesWarning` | Debug: `true`<br/>Release: `false` | 显示已知问题警告 |
| `printGodotInternalErrors` | Debug: `true`<br/>Release: `false` | 打印Godot内部错误 |
| `logLevel` | Debug: `.debug`<br/>Release: `.error` | 日志级别 |

### 性能调优配置

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `DebugTools.statusRefreshInterval` | Debug: `2.0`<br/>Release: `10.0` | 状态刷新间隔（秒） |
| `DebugTools.maxLogCacheSize` | Debug: `1000`<br/>Release: `100` | 最大日志缓存数量 |
| `Performance.godotInitDelay` | Debug: `1.0`<br/>Release: `0.5` | Godot初始化延迟（秒） |
| `Performance.godotReadyDelay` | Debug: `3.0`<br/>Release: `2.0` | Godot就绪延迟（秒） |

## 🎨 自定义配置场景

### 场景1：开发测试环境

```swift
// 启用所有调试功能
static var enableDebugPanel: Bool { return true }
static var enablePerformanceMonitoring: Bool { return true }
static var enableVerboseLogging: Bool { return true }
static var enableTripleTapDebug: Bool { return true }
```

### 场景2：生产环境（发布版本）

```swift
// 关闭调试功能，保留核心功能
static var enableDebugPanel: Bool { return false }
static var enablePerformanceMonitoring: Bool { return false }
static var enableVerboseLogging: Bool { return false }
static var enableSmartRenderingLifecycle: Bool { return true }  // 保留
```

### 场景3：Beta测试环境

```swift
// 启用部分调试功能用于收集反馈
static var enableDebugPanel: Bool { return true }
static var enablePerformanceMonitoring: Bool { return true }
static var enableVerboseLogging: Bool { return false }
static var enableTripleTapDebug: Bool { return false }
```

## 📱 Xcode 构建配置

### 1. Debug/Release 模式切换

```
Xcode → Product → Scheme → Edit Scheme → Run → Build Configuration
- Debug: 开发调试
- Release: 发布版本
```

### 2. 自定义构建配置

可以创建自定义的构建配置：

```
Xcode → Project → Configurations
- Debug
- Release
- Beta (自定义)
- Testing (自定义)
```

### 3. 验证当前配置

在应用中使用：

```swift
// 查看当前配置
BuildConfiguration.printBuildInfo()

// 检查特定配置
print("调试面板: \(BuildConfiguration.enableDebugPanel)")
print("渲染管理: \(BuildConfiguration.enableSmartRenderingLifecycle)")
```

## 🚀 最佳实践

### ✅ 推荐做法

1. **始终启用智能渲染管理** - 节省电池
2. **Debug模式启用所有调试功能** - 便于开发
3. **Release模式关闭调试功能** - 优化性能
4. **根据需要调整日志级别** - 平衡调试和性能

### ❌ 避免的做法

1. **禁用智能渲染管理** - 会导致后台耗电
2. **Release版本启用过多调试功能** - 影响性能
3. **频繁修改配置** - 保持配置稳定性

### 🔧 配置验证

使用以下方法验证配置是否正确：

```swift
// 在应用启动时检查配置
func validateConfiguration() {
    assert(BuildConfiguration.enableSmartRenderingLifecycle, "智能渲染管理必须启用")

    if BuildConfiguration.isDebugBuild {
        assert(BuildConfiguration.enableDebugPanel, "Debug模式应启用调试面板")
    }

    print("配置验证通过 ✅")
}
```

## 🆘 常见问题

### Q: 如何在Release版本中启用调试面板？

A: 修改 `enableDebugPanel` 的Release分支为 `return true`

### Q: 如何完全禁用日志输出？

A: 修改 `logLevel` 为更高级别，或设置 `enableVerboseLogging` 为 `false`

### Q: 智能渲染管理可以禁用吗？

A: 技术上可以，但强烈不推荐，会导致后台持续渲染消耗电池

### Q: 如何添加新的配置选项？

A: 在 `BuildConfiguration` 中添加新的静态变量，参考现有配置的格式

---

## 📝 配置修改检查清单

- [ ] 确认要修改的配置项
- [ ] 备份原始配置（可选）
- [ ] 修改配置值
- [ ] 重新编译测试
- [ ] 验证配置生效
- [ ] 检查不同构建模式下的行为
- [ ] 更新相关文档

---

**💡 提示：** 修改配置后需要重新编译应用才能生效。建议在不同的构建模式下都进行测试。
