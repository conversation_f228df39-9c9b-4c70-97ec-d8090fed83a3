# 🏗️ FocusFlyer 项目结构优化方案

## 📁 新的目录结构

```
focusflyer/
├── App/
│   ├── FocusflyerApp.swift           // @main 应用入口
│   ├── AppDelegate.swift             // 应用代理（如果需要）
│   └── DIContainer.swift             // 依赖注入容器
│
├── Modules/
│   ├── Home/
│   │   ├── View/
│   │   │   ├── HomeView.swift
│   │   │   └── HomeSubviews.swift
│   │   ├── ViewModel/
│   │   │   └── HomeViewModel.swift
│   │   └── Model/
│   │       └── HomeModel.swift
│   │
│   ├── Game/                         // Godot游戏模块
│   │   ├── View/
│   │   │   ├── GameView.swift
│   │   │   ├── GodotBackgroundView.swift
│   │   │   └── GameControlPanel.swift
│   │   ├── ViewModel/
│   │   │   └── GameViewModel.swift
│   │   └── Model/
│   │       ├── GameScene.swift
│   │       └── GameState.swift
│   │
│   ├── Animation/                    // Rive动画模块
│   │   ├── View/
│   │   │   └── AnimationView.swift
│   │   ├── ViewModel/
│   │   │   └── AnimationViewModel.swift
│   │   └── Model/
│   │       └── AnimationModel.swift
│   │
│   ├── Debug/                        // 调试和状态监控模块
│   │   ├── View/
│   │   │   ├── DebugPanelView.swift
│   │   │   ├── ProjectStatusView.swift
│   │   │   └── LogView.swift
│   │   ├── ViewModel/
│   │   │   └── DebugViewModel.swift
│   │   └── Model/
│   │       └── DebugModel.swift
│   │
│   └── Settings/                     // 设置模块
│       ├── View/
│       │   ├── SettingsView.swift
│       │   └── AboutView.swift
│       ├── ViewModel/
│       │   └── SettingsViewModel.swift
│       └── Model/
│           └── SettingsModel.swift
│
├── Shared/
│   ├── Components/                   // 可复用 UI 组件
│   │   ├── Buttons/
│   │   │   ├── PrimaryButton.swift
│   │   │   └── FloatingButton.swift
│   │   ├── Cards/
│   │   │   └── StatusCard.swift
│   │   ├── Overlays/
│   │   │   └── LoadingOverlay.swift
│   │   └── Common/
│   │       ├── ErrorView.swift
│   │       └── EmptyStateView.swift
│   │
│   ├── Resources/                    // 设计系统资源
│   │   ├── Colors.swift
│   │   ├── Typography.swift
│   │   ├── Spacing.swift
│   │   ├── Constants.swift
│   │   └── Assets/
│   │       ├── main.pck              // Godot资源
│   │       └── spegni_il_cervello.riv // Rive资源
│   │
│   ├── Services/                     // 业务服务层
│   │   ├── Engine/
│   │   │   ├── GodotEngineService.swift
│   │   │   └── EngineProtocols.swift
│   │   ├── Animation/
│   │   │   ├── RiveAnimationService.swift
│   │   │   └── AnimationProtocols.swift
│   │   ├── Storage/
│   │   │   ├── UserDefaultsStorage.swift
│   │   │   ├── KeychainStorage.swift
│   │   │   └── StorageProtocols.swift
│   │   ├── Logging/
│   │   │   ├── LoggingService.swift
│   │   │   └── LoggingProtocols.swift
│   │   └── Configuration/
│   │       ├── ConfigurationService.swift
│   │       └── BuildConfiguration.swift
│   │
│   ├── Utils/                        // 通用工具
│   │   ├── Extensions/
│   │   │   ├── View+Extensions.swift
│   │   │   ├── Color+Extensions.swift
│   │   │   └── String+Extensions.swift
│   │   ├── Helpers/
│   │   │   ├── DebugHelper.swift
│   │   │   └── FileHelper.swift
│   │   └── Modifiers/
│   │       └── CustomViewModifiers.swift
│   │
│   └── Environment/                  // 环境和依赖
│       ├── EnvironmentKeys.swift
│       └── DependencyInjection.swift
│
├── Data/
│   ├── Models/                       // 数据模型
│   │   ├── GameData.swift
│   │   ├── UserPreferences.swift
│   │   └── AppSettings.swift
│   ├── Storage/                      // 数据存储
│   │   ├── CoreDataManager.swift     // 如果使用 Core Data
│   │   ├── SwiftDataModels.swift     // 如果使用 SwiftData
│   │   └── DatabaseManager.swift
│   └── Repository/                   // 数据仓库层
│       ├── GameRepository.swift
│       ├── SettingsRepository.swift
│       └── RepositoryProtocols.swift
│
└── Tests/                           // 测试文件
    ├── UnitTests/
    ├── IntegrationTests/
    └── UITests/
```

## 🔄 迁移映射表

| 当前文件 | 新位置 | 说明 |
|---------|--------|------|
| `focusflyerApp.swift` | `App/FocusflyerApp.swift` | 应用入口 |
| `ContentView.swift` | `Modules/Home/View/HomeView.swift` | 主页面 |
| `GodotBackgroundView.swift` | `Modules/Game/View/GodotBackgroundView.swift` | 游戏视图 |
| `SimpleGodotInteractionView.swift` | `Modules/Game/View/GameControlPanel.swift` | 游戏控制 |
| `FloatingControlPanel.swift` | `Modules/Debug/View/DebugPanelView.swift` | 调试面板 |
| `ProjectStatusView.swift` | `Modules/Debug/View/ProjectStatusView.swift` | 状态监控 |
| `RiveFloatingView.swift` | `Modules/Animation/View/AnimationView.swift` | 动画视图 |
| `PCKManager.swift` | `Shared/Services/Engine/GodotEngineService.swift` | 引擎服务 |
| `ResourceManager.swift` | `Shared/Services/Storage/ResourceManager.swift` | 资源管理 |
| `DebugLogger.swift` | `Shared/Services/Logging/LoggingService.swift` | 日志服务 |
| `BuildConfiguration.swift` | `Shared/Services/Configuration/BuildConfiguration.swift` | 配置管理 |

## 🎯 核心优势

### 1. **模块化架构**

- 每个功能模块独立（Game, Animation, Debug, Settings）
- MVVM 架构清晰分层
- 降低耦合度，提高可维护性

### 2. **依赖注入**

- 统一的 DIContainer 管理依赖
- 便于测试和 Mock
- 清晰的依赖关系

### 3. **设计系统**

- 统一的 Colors, Typography, Spacing
- 可复用的 UI 组件
- 一致的用户体验

### 4. **服务层**

- 业务逻辑与 UI 分离
- Protocol 驱动的架构
- 易于扩展和替换

### 5. **数据层**

- 统一的数据存储管理
- Repository 模式封装数据访问
- 支持多种存储方案

## 🔧 实施计划

### Phase 1: 创建基础结构

1. 创建新的目录结构
2. 设置依赖注入容器
3. 创建基础服务和协议

### Phase 2: 迁移核心功能

1. 迁移 Godot 引擎管理
2. 迁移主要视图组件
3. 重构调试和状态监控

### Phase 3: 优化和完善

1. 实现统一的设计系统
2. 添加数据存储层
3. 完善测试覆盖

### Phase 4: 高级功能

1. 添加用户偏好设置
2. 实现游戏状态保存
3. 性能监控和分析

## 💡 技术栈建议

- **UI**: SwiftUI + Combine
- **架构**: MVVM + Repository Pattern
- **依赖注入**: 自定义 DI Container
- **数据存储**: UserDefaults + SwiftData/CoreData
- **游戏引擎**: SwiftGodotKit
- **动画**: RiveRuntime
- **测试**: XCTest + ViewInspector
