# FocusFlyer Xcode 项目配置指南

## 🎯 重构完成，现在需要在 Xcode 中完成最后的配置

### 📊 当前状态

- ✅ 代码重构已完成
- ✅ 编译错误已修复
- ✅ 目录结构已优化
- ✅ 10 个 Swift 文件重新组织
- 🔄 需要在 Xcode 中更新文件引用

### 🛠️ 在 Xcode 中需要完成的步骤

#### 1. 删除旧的文件引用

在 Xcode 项目导航器中删除以下文件的引用（如果还存在）：

- `Item.swift`
- `GlobalGodotManager.swift`
- `GodotGameView.swift`
- `SimpleGodotGameView.swift`
- `PersistentGodotGameView.swift`

⚠️ **注意**: 只删除引用，不要删除实际文件（因为文件已经在文件系统中移动了）

#### 2. 重新添加重组后的文件

按照新的目录结构添加文件到 Xcode 项目：

```
focusflyer/
├── 📁 Managers/
│   └── PCKManager.swift
├── 📁 Resources/
│   ├── 📁 Assets.xcassets/
│   ├── 📁 GodotAssets/
│   │   └── main.pck
│   ├── 📁 RiveAssets/
│   │   └── spegni_il_cervello.riv
│   └── README.md
├── 📁 Utils/
│   └── ResourceManager.swift
└── 📁 Views/
    ├── 📁 Components/
    │   ├── FloatingControlPanel.swift
    │   └── ProjectStatusView.swift
    ├── 📁 Godot/
    │   ├── GodotBackgroundView.swift
    │   └── SimpleGodotInteractionView.swift
    ├── 📁 Main/
    │   ├── ContentView.swift
    │   └── focusflyerApp.swift
    └── 📁 Rive/
        ├── RiveFloatingView.swift
        └── RiveViewRepresentable.swift
```

#### 3. 具体操作步骤

##### 步骤 1: 创建 Group 结构

在 Xcode 项目导航器中创建以下 Group：

1. 右键点击 `focusflyer` 项目根目录
2. 选择 "New Group"
3. 创建以下 Group 结构：
   - Managers
   - Resources
   - Utils
   - Views
     - Components
     - Godot
     - Main
     - Rive

##### 步骤 2: 添加文件到对应 Group

1. **Managers Group**:
   - 将 `focusflyer/Managers/PCKManager.swift` 拖拽到 Managers Group

2. **Utils Group**:
   - 将 `focusflyer/Utils/ResourceManager.swift` 拖拽到 Utils Group

3. **Views/Main Group**:
   - 将 `focusflyer/Views/Main/ContentView.swift` 拖拽到 Views/Main Group
   - 将 `focusflyer/Views/Main/focusflyerApp.swift` 拖拽到 Views/Main Group

4. **Views/Components Group**:
   - 将 `focusflyer/Views/Components/FloatingControlPanel.swift` 拖拽到 Views/Components Group
   - 将 `focusflyer/Views/Components/ProjectStatusView.swift` 拖拽到 Views/Components Group

5. **Views/Godot Group**:
   - 将 `focusflyer/Views/Godot/GodotBackgroundView.swift` 拖拽到 Views/Godot Group
   - 将 `focusflyer/Views/Godot/SimpleGodotInteractionView.swift` 拖拽到 Views/Godot Group

6. **Views/Rive Group**:
   - 将 `focusflyer/Views/Rive/RiveFloatingView.swift` 拖拽到 Views/Rive Group
   - 将 `focusflyer/Views/Rive/RiveViewRepresentable.swift` 拖拽到 Views/Rive Group

7. **Resources**:
   - 确保 `Assets.xcassets`, `GodotAssets`, `RiveAssets` 文件夹正确关联到项目
   - 检查 `main.pck` 和 `spegni_il_cervello.riv` 文件是否在项目中

##### 步骤 3: 验证 Target Membership

为每个文件检查 Target Membership：

1. 选中文件
2. 在右侧 Inspector 面板的 "Target Membership" 部分
3. 确保 `focusflyer` target 被勾选
4. 对于资源文件，确保它们也被包含在 target 中

#### 4. 编译和测试

##### 清理和构建

1. 在 Xcode 中按 `⌘ + Shift + K` 清理项目
2. 按 `⌘ + B` 构建项目
3. 解决任何剩余的编译错误

##### 常见问题和解决方案

**问题 1**: 找不到 `ResourceManager`

- **解决**: 确保 `ResourceManager.swift` 文件已添加到项目并且 Target Membership 正确

**问题 2**: 找不到 `GlobalGodotEngine`

- **解决**: 确保 `PCKManager.swift` 文件已添加到项目

**问题 3**: 资源文件找不到

- **解决**:
  - 检查 `main.pck` 和 `spegni_il_cervello.riv` 文件是否正确添加到项目
  - 确保它们在正确的 target 中
  - 验证 Bundle Resources 在 Build Phases 中包含这些文件

#### 5. 功能测试

构建成功后，测试以下功能：

1. **应用启动**: 应用能正常启动并显示 Godot 引擎初始化界面
2. **控制面板**: 左上角的控制面板能正常展开/收起
3. **Rive 动画**: 右下角的 Rive 动画能正常显示
4. **状态监控**: 三击屏幕能显示项目状态面板
5. **错误处理**: 如果资源缺失，应显示相应的错误提示

#### 6. 依赖检查

确保以下依赖正确配置：

- **SwiftGodotKit**: Godot 引擎集成
- **RiveRuntime**: Rive 动画支持
- **SwiftUI**: iOS 原生 UI 框架

### 🎯 验证清单

完成以上步骤后，请验证：

- [ ] 所有 Swift 文件都在正确的 Group 中
- [ ] 所有文件的 Target Membership 正确
- [ ] 项目能成功编译
- [ ] 应用能正常启动
- [ ] Godot 引擎初始化正常
- [ ] Rive 动画正常显示
- [ ] 悬浮控制面板功能正常
- [ ] 项目状态监控功能正常

### 🚀 下一步

项目重构和配置完成后，你可以：

1. 添加更多 Godot 场景
2. 集成更多 Rive 动画
3. 扩展 SwiftUI 控制功能
4. 优化性能和用户体验

### 💡 调试提示

如果遇到问题：

1. 查看 Xcode 控制台输出的日志信息
2. 使用项目状态监控功能检查资源状态
3. 三击屏幕显示详细的项目状态报告
4. 检查 `ResourceManager.Status.checkAllResources()` 的返回结果

## 总结

重构已经完成了代码层面的所有工作，现在只需要在 Xcode 中正确配置项目文件引用即可。按照本指南逐步操作，项目应该能够正常编译和运行。
