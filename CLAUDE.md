# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

FocusFlyer is an iOS application that combines **Godot 4.x game engine** with **SwiftUI** to create a hybrid gaming experience. The app renders Godot 3D/2D content as a background layer with native SwiftUI controls floating on top, enhanced with interactive Rive animations.

## Build Commands

```bash
# Build the project
xcodebuild -project focusflyer.xcodeproj -scheme focusflyer -sdk iphoneos -destination 'platform=iOS,name=zgh-iphone' build

# Run tests
xcodebuild -project focusflyer.xcodeproj -scheme focusflyer -destination 'platform=iOS,name=zgh-iphone' test

# Clean build
xcodebuild -project focusflyer.xcodeproj -scheme focusflyer clean
```

## Architecture

The project follows a **modular MVVM architecture** with dependency injection:

### Core Structure

- **App/**: Application entry point with DIContainer for dependency injection
- **Modules/**: Feature-based modules (Home, Game, Animation, Debug)
  - Each module has View/, ViewModel/, and Model/ subdirectories
- **Shared/**: Reusable components, services, and utilities
- **Data/**: Data models and repository layer using SwiftData + CloudKit

### Key Technology Integration

- **SwiftGodotKit**: Embeds Godot engine for game rendering
- **RiveRuntime**: Provides interactive 2D animations
- **SwiftData + CloudKit**: Data persistence with cloud sync
- **Dependency Injection**: Custom DIContainer manages all services

### Rendering Architecture

```
┌─────────────────────────────────────┐
│           SwiftUI Layer             │  ← Controls, panels, status display
├─────────────────────────────────────┤
│           Godot Layer               │  ← 3D/2D game rendering
└─────────────────────────────────────┘
```

## Key Services

- **GameEngineService**: Manages Godot engine lifecycle and PCK file loading
- **AnimationService**: Handles Rive animation loading and interaction
- **StorageService**: Provides UserDefaults and Keychain storage
- **LoggingService**: Centralized logging with different levels
- **ConfigurationService**: Manages app configuration and feature flags

## Development Workflow

### Adding Godot Content

1. Create scenes in Godot editor
2. Export as iOS PCK file to `focusflyer/Resources/GodotAssets/`
3. Update GameEngineService to reference new scenes

### Adding Rive Animations

1. Place .riv files in `focusflyer/Resources/RiveAssets/`
2. Use `RiveViewRepresentable` component in SwiftUI views
3. Configure via AnimationService

### Creating New Modules

1. Follow existing module structure: View/, ViewModel/, Model/
2. Register ViewModels in DIContainer
3. Use dependency injection for services

## Dependencies

The project uses **Swift Package Manager** exclusively:

- **SwiftGodotKit**: Godot engine integration
- **RiveRuntime** (v6.9.4): Interactive animations
- **SwiftSyntax**: Code generation support

## Requirements

- **iOS 17.0+**
- **Swift 5.9+**
- **Xcode 15.0+**

## Data Models

Uses SwiftData with CloudKit integration:

- **GameState**: Game progress and settings
- **UserPreferences**: User configuration data
- ModelContainer configured for automatic CloudKit sync

## Debug Features

The app includes comprehensive debugging tools:

- **DebugFloatingPanel**: Real-time engine status monitoring
- **LoggingService**: Structured logging with multiple levels
- Debug views show engine state, resource loading, and performance metrics

## Resource Management

- **Godot Assets**: `.pck` files in `Resources/GodotAssets/`
- **Rive Assets**: `.riv` files in `Resources/RiveAssets/`
- **App Assets**: Standard iOS assets in `Assets.xcassets`
- Design system components in `Shared/Resources/` (Colors, Typography, Spacing)
