//
//  DataModels.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Foundation
import SwiftData
import UIKit

// MARK: - Game State

@Model
class GameState: Codable {
  var id: UUID = UUID()
  var currentScene: String = "main"
  var gameProgress: Float = 0.0
  var lastPlayedDate: Date = Date()
  var isGameRunning: Bool = false
  var savedPosition: GamePosition?
  var settings: GameSettings = GameSettings()

  init(currentScene: String = "main", gameProgress: Float = 0.0) {
    self.currentScene = currentScene
    self.gameProgress = gameProgress
    self.lastPlayedDate = Date()
    self.isGameRunning = false
    self.savedPosition = nil
    self.settings = GameSettings()
  }

  // MARK: - Codable
  enum CodingKeys: String, CodingKey {
    case id, currentScene, gameProgress, lastPlayedDate, isGameRunning, savedPosition, settings
  }

  required init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    self.id = try container.decode(UUID.self, forKey: .id)
    self.currentScene = try container.decode(String.self, forKey: .currentScene)
    self.gameProgress = try container.decode(Float.self, forKey: .gameProgress)
    self.lastPlayedDate = try container.decode(Date.self, forKey: .lastPlayedDate)
    self.isGameRunning = try container.decode(Bool.self, forKey: .isGameRunning)
    self.savedPosition = try container.decodeIfPresent(GamePosition.self, forKey: .savedPosition)
    self.settings = try container.decode(GameSettings.self, forKey: .settings)
  }

  func encode(to encoder: Encoder) throws {
    var container = encoder.container(keyedBy: CodingKeys.self)
    try container.encode(id, forKey: .id)
    try container.encode(currentScene, forKey: .currentScene)
    try container.encode(gameProgress, forKey: .gameProgress)
    try container.encode(lastPlayedDate, forKey: .lastPlayedDate)
    try container.encode(isGameRunning, forKey: .isGameRunning)
    try container.encodeIfPresent(savedPosition, forKey: .savedPosition)
    try container.encode(settings, forKey: .settings)
  }
}

// MARK: - Game Position

struct GamePosition: Codable, Equatable {
  let x: Float
  let y: Float
  let z: Float
  let rotation: Float

  init(x: Float = 0, y: Float = 0, z: Float = 0, rotation: Float = 0) {
    self.x = x
    self.y = y
    self.z = z
    self.rotation = rotation
  }
}

// MARK: - Game Settings

struct GameSettings: Codable, Equatable {
  var soundEnabled: Bool = true
  var musicVolume: Float = 0.8
  var soundEffectsVolume: Float = 0.7
  var graphicsQuality: GraphicsQuality = .medium
  var autoSave: Bool = true
  var showFPS: Bool = false

  enum GraphicsQuality: String, Codable, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"

    var displayName: String {
      switch self {
      case .low: return "低"
      case .medium: return "中"
      case .high: return "高"
      }
    }
  }
}

// MARK: - Scene Preferences

struct ScenePreferences: Codable, Equatable {
  var favoriteScenes: [String] = []
  var sceneSettings: [String: SceneSetting] = [:]
  var lastAccessedScenes: [String] = []

  func getSetting(for scene: String) -> SceneSetting {
    return sceneSettings[scene] ?? SceneSetting()
  }

  mutating func updateSetting(for scene: String, setting: SceneSetting) {
    sceneSettings[scene] = setting
  }
}

struct SceneSetting: Codable, Equatable {
  var backgroundColor: String = "#000000"
  var enableParticles: Bool = true
  var animationSpeed: Float = 1.0
  var customSettings: [String: String] = [:]
}

// MARK: - User Preferences

@Model
class UserPreferences: Codable {
  var id: UUID = UUID()
  var username: String = ""
  var email: String = ""
  var theme: AppTheme = AppTheme.system
  var language: String = "zh-CN"
  var enableNotifications: Bool = true
  var enableHapticFeedback: Bool = true
  var debugMode: Bool = false
  var firstLaunch: Bool = true
  var onboardingCompleted: Bool = false
  var lastLoginDate: Date = Date()

  init() {
    self.id = UUID()
    self.username = ""
    self.email = ""
    self.theme = .system
    self.language = "zh-CN"
    self.enableNotifications = true
    self.enableHapticFeedback = true
    self.debugMode = false
    self.firstLaunch = true
    self.onboardingCompleted = false
    self.lastLoginDate = Date()
  }

  // MARK: - Codable
  enum CodingKeys: String, CodingKey {
    case id, username, email, theme, language, enableNotifications
    case enableHapticFeedback, debugMode, firstLaunch, onboardingCompleted, lastLoginDate
  }

  required init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    self.id = try container.decode(UUID.self, forKey: .id)
    self.username = try container.decode(String.self, forKey: .username)
    self.email = try container.decode(String.self, forKey: .email)
    self.theme = try container.decode(AppTheme.self, forKey: .theme)
    self.language = try container.decode(String.self, forKey: .language)
    self.enableNotifications = try container.decode(Bool.self, forKey: .enableNotifications)
    self.enableHapticFeedback = try container.decode(Bool.self, forKey: .enableHapticFeedback)
    self.debugMode = try container.decode(Bool.self, forKey: .debugMode)
    self.firstLaunch = try container.decode(Bool.self, forKey: .firstLaunch)
    self.onboardingCompleted = try container.decode(Bool.self, forKey: .onboardingCompleted)
    self.lastLoginDate = try container.decode(Date.self, forKey: .lastLoginDate)
  }

  func encode(to encoder: Encoder) throws {
    var container = encoder.container(keyedBy: CodingKeys.self)
    try container.encode(id, forKey: .id)
    try container.encode(username, forKey: .username)
    try container.encode(email, forKey: .email)
    try container.encode(theme, forKey: .theme)
    try container.encode(language, forKey: .language)
    try container.encode(enableNotifications, forKey: .enableNotifications)
    try container.encode(enableHapticFeedback, forKey: .enableHapticFeedback)
    try container.encode(debugMode, forKey: .debugMode)
    try container.encode(firstLaunch, forKey: .firstLaunch)
    try container.encode(onboardingCompleted, forKey: .onboardingCompleted)
    try container.encode(lastLoginDate, forKey: .lastLoginDate)
  }
}

// MARK: - App Settings

struct AppSettings: Codable, Equatable {
  var appVersion: String = "1.0.0"
  var buildNumber: String = "1"
  var enableAnalytics: Bool = false
  var enableCrashReporting: Bool = true
  var maxLogEntries: Int = 1000
  var enablePerformanceMonitoring: Bool = false
  var enableDebugLogging: Bool = false
  var autoBackup: Bool = true
  var backupFrequency: BackupFrequency = .daily

  enum BackupFrequency: String, Codable, CaseIterable {
    case never = "never"
    case daily = "daily"
    case weekly = "weekly"
    case monthly = "monthly"

    var displayName: String {
      switch self {
      case .never: return "从不"
      case .daily: return "每日"
      case .weekly: return "每周"
      case .monthly: return "每月"
      }
    }
  }
}

// MARK: - App Theme

enum AppTheme: String, Codable, CaseIterable {
  case light = "light"
  case dark = "dark"
  case system = "system"

  var displayName: String {
    switch self {
    case .light: return "浅色"
    case .dark: return "深色"
    case .system: return "跟随系统"
    }
  }
}

// MARK: - Animation Data

struct AnimationData: Codable, Identifiable {
  var id = UUID()
  let name: String
  let duration: TimeInterval
  let isLooping: Bool
  let filePath: String

  init(name: String, duration: TimeInterval = 1.0, isLooping: Bool = false, filePath: String) {
    self.name = name
    self.duration = duration
    self.isLooping = isLooping
    self.filePath = filePath
  }
}

// MARK: - Debug Information

struct DebugInfo: Codable {
  let appVersion: String
  let buildNumber: String
  let deviceModel: String
  let osVersion: String
  let memoryUsage: UInt64
  let batteryLevel: Float
  let thermalState: String
  let timestamp: Date

  init() {
    self.appVersion =
      Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
    self.buildNumber = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown"
    self.deviceModel = UIDevice.current.model
    self.osVersion = UIDevice.current.systemVersion
    self.memoryUsage = 0  // 需要实际计算
    self.batteryLevel = UIDevice.current.batteryLevel
    self.thermalState = "\(ProcessInfo.processInfo.thermalState.rawValue)"
    self.timestamp = Date()
  }
}
