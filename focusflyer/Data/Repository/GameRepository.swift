//
//  GameRepository.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Foundation

/// 游戏数据仓库实现 - 管理游戏状态和场景偏好的持久化
final class GameRepository: GameRepositoryProtocol {

  // MARK: - Properties
  private let storageService: StorageServiceProtocol
  private let loggingService: LoggingServiceProtocol

  // MARK: - Storage Keys
  private enum StorageKey {
    static let gameState = "game_state.json"
    static let scenePreferences = "scene_preferences"
    static let gameBackup = "game_state_backup.json"
  }

  // MARK: - Initialization

  init(storageService: StorageServiceProtocol, loggingService: LoggingServiceProtocol) {
    self.storageService = storageService
    self.loggingService = loggingService

    self.loggingService.logInfo("🎮 GameRepository 初始化完成")
  }

  // MARK: - Game State Management

  func saveGameState(_ state: GameState) async throws {
    loggingService.logInfo("💾 保存游戏状态")

    let startTime = Date()

    do {
      // 创建备份（如果存在旧状态）
      if storageService.fileExists(fileName: StorageKey.gameState) {
        try await createBackup()
      }

      // 保存新状态
      try storageService.saveToDocuments(state, fileName: StorageKey.gameState)

      // 记录性能
      let duration = Date().timeIntervalSince(startTime)
      loggingService.logPerformance(operation: "Save Game State", duration: duration)
      loggingService.logInfo("✅ 游戏状态保存成功")

    } catch {
      loggingService.logError("❌ 游戏状态保存失败: \(error.localizedDescription)")
      throw RepositoryError.saveFailed("游戏状态", error.localizedDescription)
    }
  }

  func loadGameState() async throws -> GameState? {
    loggingService.logInfo("📁 加载游戏状态")

    let startTime = Date()

    do {
      // 检查文件是否存在
      guard storageService.fileExists(fileName: StorageKey.gameState) else {
        loggingService.logInfo("ℹ️ 游戏状态文件不存在，返回默认状态")
        return createDefaultGameState()
      }

      // 加载状态
      let state = try storageService.loadFromDocuments(
        GameState.self, fileName: StorageKey.gameState)

      // 验证数据完整性
      try validateGameState(state)

      // 记录性能
      let duration = Date().timeIntervalSince(startTime)
      loggingService.logPerformance(operation: "Load Game State", duration: duration)
      loggingService.logInfo("✅ 游戏状态加载成功")

      return state

    } catch {
      loggingService.logError("❌ 游戏状态加载失败: \(error.localizedDescription)")

      // 尝试从备份恢复
      if let backupState = try? await loadBackupGameState() {
        loggingService.logInfo("📦 从备份恢复游戏状态")
        return backupState
      }

      // 返回默认状态
      loggingService.logWarning("⚠️ 返回默认游戏状态")
      return createDefaultGameState()
    }
  }

  func deleteGameState() async throws {
    loggingService.logInfo("🗑️ 删除游戏状态")

    do {
      // 创建最后备份
      if storageService.fileExists(fileName: StorageKey.gameState) {
        try await createBackup()
      }

      // 删除主文件
      try storageService.deleteFromDocuments(fileName: StorageKey.gameState)

      loggingService.logInfo("✅ 游戏状态删除成功")

    } catch {
      loggingService.logError("❌ 游戏状态删除失败: \(error.localizedDescription)")
      throw RepositoryError.deleteFailed("游戏状态", error.localizedDescription)
    }
  }

  // MARK: - Scene Preferences Management

  func saveScenePreferences(_ preferences: ScenePreferences) async throws {
    loggingService.logInfo("💾 保存场景偏好设置")

    let startTime = Date()

    storageService.setValue(preferences, forKey: StorageKey.scenePreferences)

    // 记录性能
    let duration = Date().timeIntervalSince(startTime)
    loggingService.logPerformance(operation: "Save Scene Preferences", duration: duration)
    loggingService.logInfo("✅ 场景偏好设置保存成功")
  }

  func loadScenePreferences() async throws -> ScenePreferences {
    loggingService.logInfo("📁 加载场景偏好设置")

    let startTime = Date()

    // 尝试加载存储的偏好设置
    if let preferences = storageService.getValue(
      forKey: StorageKey.scenePreferences, type: ScenePreferences.self)
    {
      let duration = Date().timeIntervalSince(startTime)
      loggingService.logPerformance(operation: "Load Scene Preferences", duration: duration)
      loggingService.logInfo("✅ 场景偏好设置加载成功")
      return preferences
    }

    // 返回默认偏好设置
    let defaultPreferences = createDefaultScenePreferences()
    loggingService.logInfo("ℹ️ 使用默认场景偏好设置")

    // 保存默认设置
    try await saveScenePreferences(defaultPreferences)

    return defaultPreferences
  }

  // MARK: - Backup Management

  private func createBackup() async throws {
    guard storageService.fileExists(fileName: StorageKey.gameState) else { return }

    loggingService.logInfo("📦 创建游戏状态备份")

    do {
      let currentState = try storageService.loadFromDocuments(
        GameState.self, fileName: StorageKey.gameState)
      try storageService.saveToDocuments(currentState, fileName: StorageKey.gameBackup)

      loggingService.logInfo("✅ 游戏状态备份创建成功")
    } catch {
      loggingService.logWarning("⚠️ 游戏状态备份创建失败: \(error.localizedDescription)")
    }
  }

  private func loadBackupGameState() async throws -> GameState? {
    guard storageService.fileExists(fileName: StorageKey.gameBackup) else { return nil }

    loggingService.logInfo("📦 加载游戏状态备份")

    do {
      let backupState = try storageService.loadFromDocuments(
        GameState.self, fileName: StorageKey.gameBackup)
      loggingService.logInfo("✅ 游戏状态备份加载成功")
      return backupState
    } catch {
      loggingService.logError("❌ 游戏状态备份加载失败: \(error.localizedDescription)")
      return nil
    }
  }

  // MARK: - Data Validation

  private func validateGameState(_ state: GameState) throws {
    // 验证场景名称
    guard !state.currentScene.isEmpty else {
      throw RepositoryError.invalidData("游戏状态", "场景名称不能为空")
    }

    // 验证进度值
    guard state.gameProgress >= 0.0 && state.gameProgress <= 1.0 else {
      throw RepositoryError.invalidData("游戏状态", "游戏进度值无效")
    }

    // 验证日期
    guard state.lastPlayedDate <= Date() else {
      throw RepositoryError.invalidData("游戏状态", "最后游戏时间无效")
    }

    loggingService.logDebug("✅ 游戏状态数据验证通过")
  }

  // MARK: - Default Data Creation

  private func createDefaultGameState() -> GameState {
    let defaultState = GameState(currentScene: "main", gameProgress: 0.0)
    loggingService.logInfo("🆕 创建默认游戏状态")
    return defaultState
  }

  private func createDefaultScenePreferences() -> ScenePreferences {
    var preferences = ScenePreferences()

    // 设置默认收藏场景
    preferences.favoriteScenes = ["main", "game"]

    // 设置默认场景设置
    preferences.updateSetting(
      for: "main",
      setting: SceneSetting(
        backgroundColor: "#000000",
        enableParticles: true,
        animationSpeed: 1.0
      ))

    preferences.updateSetting(
      for: "game",
      setting: SceneSetting(
        backgroundColor: "#1a1a1a",
        enableParticles: true,
        animationSpeed: 1.2
      ))

    loggingService.logInfo("🆕 创建默认场景偏好设置")
    return preferences
  }
}

// MARK: - Statistics and Analytics

extension GameRepository {

  struct GameStatistics {
    let totalPlayTime: TimeInterval
    let sessionsCount: Int
    let averageSessionLength: TimeInterval
    let favoriteScene: String?
    let lastPlayedDate: Date?
    let gameProgress: Float
  }

  func getGameStatistics() async throws -> GameStatistics {
    loggingService.logInfo("📊 获取游戏统计数据")

    do {
      let gameState = try await loadGameState()
      let scenePreferences = try await loadScenePreferences()

      // 计算统计数据
      let statistics = GameStatistics(
        totalPlayTime: calculateTotalPlayTime(gameState),
        sessionsCount: calculateSessionsCount(gameState),
        averageSessionLength: calculateAverageSessionLength(gameState),
        favoriteScene: scenePreferences.favoriteScenes.first,
        lastPlayedDate: gameState?.lastPlayedDate,
        gameProgress: gameState?.gameProgress ?? 0.0
      )

      loggingService.logInfo("✅ 游戏统计数据获取成功")
      return statistics

    } catch {
      loggingService.logError("❌ 游戏统计数据获取失败: \(error.localizedDescription)")
      throw error
    }
  }

  private func calculateTotalPlayTime(_ gameState: GameState?) -> TimeInterval {
    // 简化实现，实际应该累计所有游戏会话时间
    return gameState?.lastPlayedDate.timeIntervalSinceNow ?? 0
  }

  private func calculateSessionsCount(_ gameState: GameState?) -> Int {
    // 简化实现，实际应该跟踪游戏会话数量
    return gameState != nil ? 1 : 0
  }

  private func calculateAverageSessionLength(_ gameState: GameState?) -> TimeInterval {
    // 简化实现
    let totalTime = calculateTotalPlayTime(gameState)
    let sessions = calculateSessionsCount(gameState)
    return sessions > 0 ? totalTime / TimeInterval(sessions) : 0
  }
}

// MARK: - Data Migration

extension GameRepository {

  func migrateData(from oldVersion: String, to newVersion: String) async throws {
    loggingService.logInfo("🔄 游戏数据迁移: \(oldVersion) -> \(newVersion)")

    // 这里可以实现数据版本迁移逻辑
    // 例如：字段重命名、数据结构变更等

    switch (oldVersion, newVersion) {
    case ("1.0.0", "1.1.0"):
      try await migrateFrom1_0_0To1_1_0()
    default:
      loggingService.logInfo("ℹ️ 无需数据迁移")
    }

    loggingService.logInfo("✅ 游戏数据迁移完成")
  }

  private func migrateFrom1_0_0To1_1_0() async throws {
    // 示例迁移逻辑
    loggingService.logInfo("🔄 执行 1.0.0 -> 1.1.0 数据迁移")

    // 可以在这里添加具体的迁移步骤
    // 例如：添加新字段、转换数据格式等
  }
}

// MARK: - Repository Error

enum RepositoryError: LocalizedError {
  case saveFailed(String, String)
  case loadFailed(String, String)
  case deleteFailed(String, String)
  case invalidData(String, String)
  case migrationFailed(String)
  case dataCorruption(String)

  var errorDescription: String? {
    switch self {
    case .saveFailed(let type, let reason):
      return "保存\(type)失败: \(reason)"
    case .loadFailed(let type, let reason):
      return "加载\(type)失败: \(reason)"
    case .deleteFailed(let type, let reason):
      return "删除\(type)失败: \(reason)"
    case .invalidData(let type, let reason):
      return "\(type)数据无效: \(reason)"
    case .migrationFailed(let reason):
      return "数据迁移失败: \(reason)"
    case .dataCorruption(let reason):
      return "数据损坏: \(reason)"
    }
  }
}
