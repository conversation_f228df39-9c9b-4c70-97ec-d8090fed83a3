//
//  SettingsRepository.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Foundation

/// 设置数据仓库实现 - 管理用户偏好和应用设置的持久化
final class SettingsRepository: SettingsRepositoryProtocol {

  // MARK: - Properties
  private let storageService: StorageServiceProtocol
  private let loggingService: LoggingServiceProtocol

  // MARK: - Storage Keys
  private enum StorageKey {
    static let userPreferences = "user_preferences.json"
    static let appSettings = "app_settings"
    static let userPreferencesLegacy = "user_preferences_legacy"
    static let settingsBackup = "settings_backup.json"
  }

  // MARK: - Initialization

  init(storageService: StorageServiceProtocol, loggingService: LoggingServiceProtocol) {
    self.storageService = storageService
    self.loggingService = loggingService

    self.loggingService.logInfo("⚙️ SettingsRepository 初始化完成")
  }

  // MARK: - User Preferences Management

  func saveUserPreferences(_ preferences: UserPreferences) async throws {
    loggingService.logInfo("💾 保存用户偏好设置")

    let startTime = Date()

    do {
      // 创建备份（如果存在）
      if storageService.fileExists(fileName: StorageKey.userPreferences) {
        try await createUserPreferencesBackup()
      }

      // 保存到文档目录（安全性更高）
      try storageService.saveToDocuments(preferences, fileName: StorageKey.userPreferences)

      // 记录性能
      let duration = Date().timeIntervalSince(startTime)
      loggingService.logPerformance(operation: "Save User Preferences", duration: duration)
      loggingService.logInfo("✅ 用户偏好设置保存成功")

    } catch {
      loggingService.logError("❌ 用户偏好设置保存失败: \(error.localizedDescription)")
      throw RepositoryError.saveFailed("用户偏好设置", error.localizedDescription)
    }
  }

  func loadUserPreferences() async throws -> UserPreferences {
    loggingService.logInfo("📁 加载用户偏好设置")

    let startTime = Date()

    do {
      // 首先尝试从文档目录加载
      if storageService.fileExists(fileName: StorageKey.userPreferences) {
        let preferences = try storageService.loadFromDocuments(
          UserPreferences.self, fileName: StorageKey.userPreferences)

        // 验证数据
        try validateUserPreferences(preferences)

        let duration = Date().timeIntervalSince(startTime)
        loggingService.logPerformance(operation: "Load User Preferences", duration: duration)
        loggingService.logInfo("✅ 用户偏好设置加载成功")

        return preferences
      }

      // 尝试从遗留存储位置迁移
      if let legacyPreferences = storageService.getValue(
        forKey: StorageKey.userPreferencesLegacy, type: UserPreferences.self)
      {
        loggingService.logInfo("🔄 从遗留位置迁移用户偏好设置")

        // 迁移到新位置
        try await saveUserPreferences(legacyPreferences)

        // 清理遗留数据
        storageService.removeValue(forKey: StorageKey.userPreferencesLegacy)

        return legacyPreferences
      }

      // 创建默认偏好设置
      let defaultPreferences = createDefaultUserPreferences()
      loggingService.logInfo("🆕 创建默认用户偏好设置")

      // 保存默认设置
      try await saveUserPreferences(defaultPreferences)

      return defaultPreferences

    } catch {
      loggingService.logError("❌ 用户偏好设置加载失败: \(error.localizedDescription)")

      // 尝试从备份恢复
      if let backupPreferences = try? await loadUserPreferencesBackup() {
        loggingService.logInfo("📦 从备份恢复用户偏好设置")
        return backupPreferences
      }

      // 返回默认设置
      loggingService.logWarning("⚠️ 返回默认用户偏好设置")
      return createDefaultUserPreferences()
    }
  }

  // MARK: - App Settings Management

  func saveAppSettings(_ settings: AppSettings) async throws {
    loggingService.logInfo("💾 保存应用设置")

    let startTime = Date()

    // 使用 UserDefaults 存储应用设置（便于系统集成）
    storageService.setValue(settings, forKey: StorageKey.appSettings)

    // 记录性能
    let duration = Date().timeIntervalSince(startTime)
    loggingService.logPerformance(operation: "Save App Settings", duration: duration)
    loggingService.logInfo("✅ 应用设置保存成功")
  }

  func loadAppSettings() async throws -> AppSettings {
    loggingService.logInfo("📁 加载应用设置")

    let startTime = Date()

    // 尝试加载存储的设置
    if let settings = storageService.getValue(
      forKey: StorageKey.appSettings, type: AppSettings.self)
    {
      // 验证数据
      try validateAppSettings(settings)

      let duration = Date().timeIntervalSince(startTime)
      loggingService.logPerformance(operation: "Load App Settings", duration: duration)
      loggingService.logInfo("✅ 应用设置加载成功")

      return settings
    }

    // 创建默认设置
    let defaultSettings = createDefaultAppSettings()
    loggingService.logInfo("🆕 创建默认应用设置")

    // 保存默认设置
    try await saveAppSettings(defaultSettings)

    return defaultSettings
  }

  // MARK: - Backup Management

  private func createUserPreferencesBackup() async throws {
    guard storageService.fileExists(fileName: StorageKey.userPreferences) else { return }

    loggingService.logInfo("📦 创建用户偏好设置备份")

    do {
      let currentPreferences = try storageService.loadFromDocuments(
        UserPreferences.self, fileName: StorageKey.userPreferences)

      // 创建备份文件名（包含时间戳）
      let timestamp = Int(Date().timeIntervalSince1970)
      let backupFileName = "user_preferences_backup_\(timestamp).json"

      try storageService.saveToDocuments(currentPreferences, fileName: backupFileName)

      loggingService.logInfo("✅ 用户偏好设置备份创建成功")
    } catch {
      loggingService.logWarning("⚠️ 用户偏好设置备份创建失败: \(error.localizedDescription)")
    }
  }

  private func loadUserPreferencesBackup() async throws -> UserPreferences? {
    // 查找最新的备份文件
    guard
      let documentsDirectory = FileManager.default.urls(
        for: .documentDirectory, in: .userDomainMask
      ).first
    else {
      return nil
    }

    do {
      let files = try FileManager.default.contentsOfDirectory(
        at: documentsDirectory, includingPropertiesForKeys: [.creationDateKey])

      let backupFiles = files.filter { $0.lastPathComponent.hasPrefix("user_preferences_backup_") }
        .sorted { file1, file2 in
          let date1 = try? file1.resourceValues(forKeys: [.creationDateKey]).creationDate
          let date2 = try? file2.resourceValues(forKeys: [.creationDateKey]).creationDate
          return (date1 ?? Date.distantPast) > (date2 ?? Date.distantPast)
        }

      guard let latestBackup = backupFiles.first else { return nil }

      loggingService.logInfo("📦 加载用户偏好设置备份: \(latestBackup.lastPathComponent)")

      let backupPreferences = try storageService.loadFromDocuments(
        UserPreferences.self, fileName: latestBackup.lastPathComponent)
      loggingService.logInfo("✅ 用户偏好设置备份加载成功")

      return backupPreferences

    } catch {
      loggingService.logError("❌ 用户偏好设置备份加载失败: \(error.localizedDescription)")
      return nil
    }
  }

  // MARK: - Data Validation

  private func validateUserPreferences(_ preferences: UserPreferences) throws {
    // 验证主题设置
    guard AppTheme.allCases.contains(preferences.theme) else {
      throw RepositoryError.invalidData("用户偏好", "主题设置无效")
    }

    // 验证语言代码
    guard !preferences.language.isEmpty else {
      throw RepositoryError.invalidData("用户偏好", "语言设置不能为空")
    }

    // 验证登录日期
    guard preferences.lastLoginDate <= Date() else {
      throw RepositoryError.invalidData("用户偏好", "最后登录时间无效")
    }

    loggingService.logDebug("✅ 用户偏好设置数据验证通过")
  }

  private func validateAppSettings(_ settings: AppSettings) throws {
    // 验证版本号格式
    guard !settings.appVersion.isEmpty else {
      throw RepositoryError.invalidData("应用设置", "应用版本号不能为空")
    }

    // 验证构建号
    guard !settings.buildNumber.isEmpty else {
      throw RepositoryError.invalidData("应用设置", "构建号不能为空")
    }

    // 验证日志条目数量限制
    guard settings.maxLogEntries > 0 && settings.maxLogEntries <= 10000 else {
      throw RepositoryError.invalidData("应用设置", "最大日志条目数量无效")
    }

    loggingService.logDebug("✅ 应用设置数据验证通过")
  }

  // MARK: - Default Data Creation

  private func createDefaultUserPreferences() -> UserPreferences {
    let preferences = UserPreferences()

    // 设置默认值
    preferences.theme = .system
    preferences.language = Locale.current.language.languageCode?.identifier ?? "zh-CN"
    preferences.enableNotifications = true
    preferences.enableHapticFeedback = true
    preferences.debugMode = false
    preferences.firstLaunch = true
    preferences.onboardingCompleted = false
    preferences.lastLoginDate = Date()

    loggingService.logInfo("🆕 创建默认用户偏好设置")
    return preferences
  }

  private func createDefaultAppSettings() -> AppSettings {
    var settings = AppSettings()

    // 从 Bundle 获取版本信息
    settings.appVersion =
      Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    settings.buildNumber = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"

    // 设置默认配置
    settings.enableAnalytics = false
    settings.enableCrashReporting = true
    settings.maxLogEntries = 1000
    settings.enablePerformanceMonitoring = false
    settings.enableDebugLogging = false
    settings.autoBackup = true
    settings.backupFrequency = .daily

    loggingService.logInfo("🆕 创建默认应用设置")
    return settings
  }
}

// MARK: - Settings Sync

extension SettingsRepository {

  /// 同步设置到 iCloud（如果启用）
  func syncToCloud() async throws {
    loggingService.logInfo("☁️ 开始同步设置到 iCloud")

    // 检查 iCloud 可用性
    guard FileManager.default.ubiquityIdentityToken != nil else {
      loggingService.logWarning("⚠️ iCloud 不可用，跳过同步")
      return
    }

    do {
      _ = try await loadUserPreferences()
      _ = try await loadAppSettings()
      loggingService.logInfo("✅ 数据完整性检查通过")
    } catch {
      loggingService.logError("❌ 数据完整性检查失败: \(error)")
      throw RepositoryError.dataCorruption("数据完整性检查失败")
    }

    // 这里可以实现 iCloud 同步逻辑
    // 例如使用 NSUbiquitousKeyValueStore 或 CloudKit

    loggingService.logInfo("✅ 设置同步到 iCloud 成功")

  }

  /// 从 iCloud 同步设置
  func syncFromCloud() async throws {
    loggingService.logInfo("☁️ 开始从 iCloud 同步设置")

    // 检查 iCloud 可用性
    guard FileManager.default.ubiquityIdentityToken != nil else {
      loggingService.logWarning("⚠️ iCloud 不可用，跳过同步")
      return
    }

    // 这里可以实现从 iCloud 同步的逻辑

    loggingService.logInfo("✅ 从 iCloud 同步设置成功")
  }
}

// MARK: - Settings Export/Import

extension SettingsRepository {

  struct SettingsExport: Codable {
    let userPreferences: UserPreferences
    let appSettings: AppSettings
    let exportDate: Date
    let appVersion: String
  }

  /// 导出所有设置
  func exportSettings() async throws -> SettingsExport {
    loggingService.logInfo("📤 导出应用设置")

    do {
      let preferences = try await loadUserPreferences()
      let settings = try await loadAppSettings()

      let export = SettingsExport(
        userPreferences: preferences,
        appSettings: settings,
        exportDate: Date(),
        appVersion: settings.appVersion
      )

      loggingService.logInfo("✅ 应用设置导出成功")
      return export

    } catch {
      loggingService.logError("❌ 应用设置导出失败: \(error.localizedDescription)")
      throw error
    }
  }

  /// 导入设置
  func importSettings(_ export: SettingsExport) async throws {
    loggingService.logInfo("📥 导入应用设置")

    do {
      // 验证导入的数据
      try validateUserPreferences(export.userPreferences)
      try validateAppSettings(export.appSettings)

      // 创建备份
      try await createUserPreferencesBackup()

      // 导入设置
      try await saveUserPreferences(export.userPreferences)
      try await saveAppSettings(export.appSettings)

      loggingService.logInfo("✅ 应用设置导入成功")

    } catch {
      loggingService.logError("❌ 应用设置导入失败: \(error.localizedDescription)")
      throw error
    }
  }

  /// 重置所有设置到默认值
  func resetToDefaults() async throws {
    loggingService.logInfo("🔄 重置所有设置到默认值")

    do {
      // 创建备份
      try await createUserPreferencesBackup()

      // 创建默认设置
      let defaultPreferences = createDefaultUserPreferences()
      let defaultSettings = createDefaultAppSettings()

      // 保存默认设置
      try await saveUserPreferences(defaultPreferences)
      try await saveAppSettings(defaultSettings)

      loggingService.logInfo("✅ 设置重置到默认值成功")

    } catch {
      loggingService.logError("❌ 设置重置失败: \(error.localizedDescription)")
      throw error
    }
  }
}

// MARK: - Settings Analytics

extension SettingsRepository {

  struct SettingsStatistics {
    let userPreferencesSize: UInt64
    let appSettingsSize: UInt64
    let totalBackupFiles: Int
    let lastModifiedDate: Date?
    let syncEnabled: Bool
  }

  func getSettingsStatistics() async -> SettingsStatistics {
    loggingService.logInfo("📊 获取设置统计信息")

    let userPreferencesSize = storageService.getFileSize(fileName: StorageKey.userPreferences) ?? 0
    let appSettingsSize = estimateUserDefaultsSize()
    let backupCount = countBackupFiles()
    let lastModified = getLastModifiedDate()
    let syncEnabled = FileManager.default.ubiquityIdentityToken != nil

    return SettingsStatistics(
      userPreferencesSize: UInt64(userPreferencesSize),
      appSettingsSize: appSettingsSize,
      totalBackupFiles: backupCount,
      lastModifiedDate: lastModified,
      syncEnabled: syncEnabled
    )
  }

  private func estimateUserDefaultsSize() -> UInt64 {
    // 估算 UserDefaults 中应用设置的大小
    let defaults = UserDefaults.standard.dictionaryRepresentation()
    let appSettingsData = defaults.filter { key, _ in
      key.hasPrefix("app_") || key.contains("setting")
    }

    do {
      let data = try JSONSerialization.data(withJSONObject: appSettingsData)
      return UInt64(data.count)
    } catch {
      return 0
    }
  }

  private func countBackupFiles() -> Int {
    guard
      let documentsDirectory = FileManager.default.urls(
        for: .documentDirectory, in: .userDomainMask
      ).first
    else {
      return 0
    }

    do {
      let files = try FileManager.default.contentsOfDirectory(
        at: documentsDirectory, includingPropertiesForKeys: nil)
      return files.filter { $0.lastPathComponent.hasPrefix("user_preferences_backup_") }.count
    } catch {
      return 0
    }
  }

  private func getLastModifiedDate() -> Date? {
    guard
      let documentsDirectory = FileManager.default.urls(
        for: .documentDirectory, in: .userDomainMask
      ).first
    else {
      return nil
    }

    let preferencesFile = documentsDirectory.appendingPathComponent(StorageKey.userPreferences)

    do {
      let attributes = try FileManager.default.attributesOfItem(atPath: preferencesFile.path)
      return attributes[.modificationDate] as? Date
    } catch {
      return nil
    }
  }
}
