# FocusFlyer 资源文件管理指南

## 目录结构

```
focusflyer/
├── Resources/              # 所有资源文件
│   ├── GodotAssets/        # Godot 引擎资源
│   │   └── main.pck        # 主要的 Godot 游戏包
│   ├── RiveAssets/         # Rive 动画资源
│   │   └── spegni_il_cervello.riv  # Rive 动画文件
│   └── Assets.xcassets/    # App 图标和颜色资源
├── Views/                  # SwiftUI 视图
│   ├── Main/               # 主视图
│   │   ├── ContentView.swift
│   │   └── focusflyerApp.swift
│   ├── Components/         # 可复用组件
│   │   └── FloatingControlPanel.swift
│   ├── Godot/              # Godot 相关视图
│   │   ├── GodotBackgroundView.swift
│   │   └── SimpleGodotInteractionView.swift
│   └── Rive/               # Rive 相关视图
│       ├── RiveFloatingView.swift
│       └── RiveViewRepresentable.swift
├── Managers/               # 管理器类
│   └── PCKManager.swift    # Godot 引擎管理器
└── Utils/                  # 工具类（预留）
```

## 资源文件说明

### Godot 资源文件 (GodotAssets/)

- **main.pck**: 主要的 Godot 游戏包，包含游戏场景、脚本和资源
- **存放位置**: `focusflyer/Resources/GodotAssets/`
- **使用方式**: 通过 `GlobalGodotEngine` 管理器加载和控制

### Rive 动画资源 (RiveAssets/)

- **spegni_il_cervello.riv**: 交互式 Rive 动画文件
- **存放位置**: `focusflyer/Resources/RiveAssets/`
- **使用方式**: 通过 `RiveViewModel` 加载，在 `RiveFloatingView` 中展示

### 应用资源 (Assets.xcassets/)

- **AppIcon.appiconset**: 应用图标
- **AccentColor.colorset**: 应用主题色
- **存放位置**: `focusflyer/Resources/Assets.xcassets/`

## 添加新资源文件

### 添加 Godot 资源

1. 在 Godot 编辑器中导出项目为 iOS PCK 文件
2. 将 PCK 文件放入 `focusflyer/Resources/GodotAssets/` 目录
3. 在 Xcode 中添加文件到项目
4. 更新 `PCKManager.swift` 中的文件名引用

### 添加 Rive 动画

1. 将 .riv 文件放入 `focusflyer/Resources/RiveAssets/` 目录
2. 在 Xcode 中添加文件到项目
3. 在相应的 SwiftUI 视图中通过 `RiveViewModel(fileName: "filename")` 加载

### 添加应用资源

1. 将图片、颜色等资源添加到 `focusflyer/Resources/Assets.xcassets/`
2. 在 SwiftUI 中通过 `Image("imageName")` 或 `Color("colorName")` 使用

## 项目架构特点

- **Godot 引擎**: 作为底层渲染基础，提供 3D/2D 游戏场景
- **SwiftUI 悬浮界面**: 在 Godot 渲染层之上提供原生 iOS 控件
- **Rive 动画**: 提供高质量的交互式 2D 动画
- **模块化设计**: 按功能分类组织代码，便于维护和扩展
