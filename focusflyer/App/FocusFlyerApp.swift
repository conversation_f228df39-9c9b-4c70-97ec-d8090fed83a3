import SwiftData
import SwiftUI

@main
struct FocusFlyerApp: App {

  private let container = DIContainer.shared

  private let modelContainer: ModelContainer = {
    do {
      let schema = Schema([
        GameState.self,
        UserPreferences.self,
          // AppSettings.self, // 已移除：不是Model类型
      ])

      let configuration = ModelConfiguration(
        schema: schema,
        isStoredInMemoryOnly: false,
        cloudKitDatabase: .automatic
      )

      return try ModelContainer(
        for: schema,
        configurations: [configuration]
      )
    } catch {
      fatalError("Failed to create ModelContainer: \(error)")
    }
  }()

  init() {
    setupApp()
  }

  var body: some Scene {
    WindowGroup {
      HomeView()
        .environmentObject(container)
        .modelContainer(modelContainer)
        .onAppear {
          setupDependencies()
        }
    }
  }

  private func setupApp() {
    // container.loggingService.logAppLaunch() // 已移除：无此方法
  }

  private func setupDependencies() {
    // if container.configurationService.isFeatureEnabled("game_engine_auto_init") {
    //   Task {
    //     await container.gameEngineService.initialize()
    //   }
    // }
    // 直接依赖GameEngineService的自动初始化逻辑，无需手动调用

    Task {
      do {
        _ = try await container.settingsRepository.loadUserPreferences()  // 只处理副作用，不跨actor传递结果
      } catch {
        container.loggingService.logError("加载用户偏好设置失败: \(error)")
      }
    }

    // container.animationService.preloadAnimations() // 已移除：协议无此方法，且构造时已自动预加载
  }
}
