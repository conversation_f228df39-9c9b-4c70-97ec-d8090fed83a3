//
//  DIContainer.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Foundation
import SwiftUI

/// 依赖注入容器 - 管理整个应用的服务依赖
@MainActor
final class DIContainer: ObservableObject {

  // MARK: - Singleton
  static let shared = DIContainer()

  // MARK: - Services
  @Published private(set) var gameEngineService: GameEngineService
  @Published private(set) var storageService: StorageServiceProtocol
  @Published private(set) var loggingService: LoggingServiceProtocol
  @Published private(set) var configurationService: ConfigurationServiceProtocol
  @Published private(set) var animationService: any AnimationServiceProtocol

  // MARK: - Repositories
  private(set) var gameRepository: GameRepositoryProtocol
  private(set) var settingsRepository: SettingsRepositoryProtocol

  private init() {
    // 首先初始化基础服务
    let loggingService = LoggingService()
    let configurationService = ConfigurationService()
    let storageService = StorageService()

    // 设置服务属性
    self.loggingService = loggingService
    self.configurationService = configurationService
    self.storageService = storageService

    // 初始化依赖于基础服务的服务
    let gameEngineService = GameEngineService(
      loggingService: loggingService,
      configurationService: configurationService
    )
    self.gameEngineService = gameEngineService
    self.animationService = AnimationService(loggingService: loggingService)

    // 初始化仓库
    self.gameRepository = GameRepository(
      storageService: storageService,
      loggingService: loggingService
    )
    self.settingsRepository = SettingsRepository(
      storageService: storageService,
      loggingService: loggingService
    )

    loggingService.logInfo("🔧 依赖注入容器初始化完成")
  }
}

// MARK: - Environment Key
private struct DIContainerKey: EnvironmentKey {
  @MainActor static let defaultValue = DIContainer.shared
}

extension EnvironmentValues {
  var diContainer: DIContainer {
    get { self[DIContainerKey.self] }
    set { self[DIContainerKey.self] = newValue }
  }
}

// MARK: - View Extension
extension View {
  func withDependencyInjection() -> some View {
    self.environmentObject(DIContainer.shared)
      .environment(\.diContainer, DIContainer.shared)
  }
}
