//
//  AnimationFloatingView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import RiveRuntime
import SwiftUI

/// 动画悬浮视图 - Rive动画展示
struct AnimationFloatingView: View {
  @StateObject private var viewModel = AnimationViewModel()
  @EnvironmentObject private var diContainer: DIContainer
  @StateObject private var panelManager = FloatingPanelManager()

  @State private var showAnimationView = false
  
  // 估算的面板大小
  private let estimatedPanelSize = CGSize(width: 200, height: 250)

  var body: some View {
    GeometryReader { geometry in
      ZStack {
        // 动画展示区域 - 独立的视图
        if showAnimationView {
          animationDisplayArea
            .transition(
              .asymmetric(
                insertion: .move(edge: .bottom).combined(with: .opacity),
                removal: .move(edge: .bottom).combined(with: .opacity)
              ))
        }

        // 悬浮控制按钮
        floatingButton
          .position(panelManager.position)
          .floatingDragGesture(
            manager: panelManager,
            estimatedPanelSize: estimatedPanelSize
          )
      }
      .onAppear {
        viewModel.onAppear()
        panelManager.setInitialPosition(
          preference: .bottomRight,
          buttonSize: 60,
          geometry: geometry
        )
      }
    }
  }

  @ViewBuilder
  private var floatingButton: some View {
    VStack {
      if panelManager.isExpanded {
        expandedControlPanel
      } else {
        collapsedButton
      }
    }
    .animation(FloatingPositionHelper.expandAnimation, value: panelManager.isExpanded)
  }

  @ViewBuilder
  private var collapsedButton: some View {
    Button(action: {
      // 只有在未拖拽时才响应点击
      if !panelManager.dragState.shouldPreventClick {
        withAnimation(FloatingPositionHelper.expandAnimation) {
          panelManager.toggleExpanded(estimatedPanelSize: estimatedPanelSize)
        }
      }
    }) {
      ZStack {
        Circle()
          .fill(.ultraThinMaterial)
          .frame(width: 60, height: 60)
          .shadow(radius: 10)

        Image(systemName: "play.circle.fill")
          .font(.title2)
          .foregroundColor(.white)
      }
    }
    .buttonStyle(PlainButtonStyle())
  }

  @ViewBuilder
  private var expandedControlPanel: some View {
    VStack(spacing: Spacing.md) {
      // 动画控制面板
      controlPanel

      // 折叠按钮
      Button(action: {
        // 只有在未拖拽时才响应点击
        if !panelManager.dragState.shouldPreventClick {
          withAnimation(FloatingPositionHelper.expandAnimation) {
            panelManager.toggleExpanded(estimatedPanelSize: estimatedPanelSize)
          }
        }
      }) {
        ZStack {
          Circle()
            .fill(.ultraThinMaterial)
            .frame(width: 60, height: 60)
            .shadow(radius: 10)

          Image(systemName: "chevron.down")
            .font(.title2)
            .foregroundColor(.white)
        }
      }
      .buttonStyle(PlainButtonStyle())
    }
    .padding(Spacing.md)
    .background(.regularMaterial)
    .cornerRadius(Spacing.Container.cornerRadius)
    .shadow(radius: 20)
    .frame(maxWidth: panelLayout?.size.width ?? 200)
    .offset(x: panelLayout?.offset.x ?? 0, y: panelLayout?.offset.y ?? 0)
  }
  
  // 计算面板布局
  private var panelLayout: (position: CGPoint, size: CGSize, offset: CGPoint)? {
    panelManager.getPanelLayout(estimatedSize: estimatedPanelSize)
  }

  @ViewBuilder
  private var controlPanel: some View {
    VStack(spacing: Spacing.sm) {
      Text("🎨 动画控制")
        .font(.headline)
        .foregroundColor(.primary)

      // 播放/暂停按钮
      HStack(spacing: Spacing.md) {
        Button(action: {
          if viewModel.isAnimationPlaying {
            viewModel.pauseAnimation()
          } else {
            viewModel.playDefaultAnimation()
          }
        }) {
          Image(systemName: viewModel.isAnimationPlaying ? "pause.fill" : "play.fill")
            .font(.title3)
            .foregroundColor(.white)
            .frame(width: 44, height: 44)
            .background(.blue)
            .clipShape(Circle())
        }
        .buttonStyle(PlainButtonStyle())

        Button(action: {
          viewModel.stopAnimation()
        }) {
          Image(systemName: "stop.fill")
            .font(.title3)
            .foregroundColor(.white)
            .frame(width: 44, height: 44)
            .background(.red)
            .clipShape(Circle())
        }
        .buttonStyle(PlainButtonStyle())
      }

      // 显示/隐藏动画区域按钮
      Button(action: {
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
          showAnimationView.toggle()
        }
      }) {
        HStack {
          Image(systemName: showAnimationView ? "eye.slash.fill" : "eye.fill")
            .font(.caption)
          Text(showAnimationView ? "隐藏动画" : "显示动画")
            .font(.caption)
        }
        .foregroundColor(.white)
        .padding(.horizontal, Spacing.md)
        .padding(.vertical, Spacing.sm)
        .background(.green)
        .cornerRadius(Spacing.sm)
      }
      .buttonStyle(PlainButtonStyle())

      // 状态信息
      if let currentAnimation = viewModel.currentAnimation {
        Text("当前: \(currentAnimation)")
          .font(.caption)
          .foregroundColor(.secondary)
      }
    }
  }

  @ViewBuilder
  private var animationDisplayArea: some View {
    VStack {
      Spacer()

      // 动画展示区域 - 位于屏幕底部
      VStack(spacing: Spacing.md) {
        // 标题栏
        HStack {
          Text("🎭 Rive 动画")
            .font(.headline)
            .foregroundColor(.primary)

          Spacer()

          Button(action: {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
              showAnimationView = false
            }
          }) {
            Image(systemName: "xmark.circle.fill")
              .font(.title3)
              .foregroundColor(.secondary)
          }
          .buttonStyle(PlainButtonStyle())
        }

        // Rive 动画展示区域
        if viewModel.isAnimationPlaying, let animationName = viewModel.currentAnimation {
          RiveViewRepresentable(fileName: animationName)
            .frame(height: 200)
            .background(Color.black.opacity(0.1))
            .cornerRadius(Spacing.Container.cornerRadius)
            .clipped()
        } else {
          // 占位视图
          RoundedRectangle(cornerRadius: Spacing.Container.cornerRadius)
            .fill(Color.gray.opacity(0.2))
            .frame(height: 200)
            .overlay(
              VStack {
                Image(systemName: "play.circle")
                  .font(.largeTitle)
                  .foregroundColor(.gray)
                Text("点击播放按钮开始动画")
                  .font(.caption)
                  .foregroundColor(.gray)
              }
            )
        }
      }
      .padding(Spacing.lg)
      .background(.regularMaterial)
      .cornerRadius(Spacing.Container.cornerRadius)
      .shadow(radius: 20)
      .padding(.horizontal, Spacing.lg)
      .padding(.bottom, Spacing.xl)  // 确保不被底部安全区域遮挡
    }
  }


}
