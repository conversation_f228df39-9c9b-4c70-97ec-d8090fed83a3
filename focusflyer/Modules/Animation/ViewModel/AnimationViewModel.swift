//
//  AnimationViewModel.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Foundation
import SwiftUI

/// 动画模块视图模型
@MainActor
final class AnimationViewModel: ObservableObject {

  // MARK: - Published Properties
  @Published var isAnimationPlaying: Bool = false
  @Published var currentAnimation: String?
  @Published var availableAnimations: [String] = []

  // MARK: - Dependencies
  private var animationService: (any AnimationServiceProtocol)?
  private var loggingService: LoggingServiceProtocol?

  // MARK: - Constants
  private let defaultAnimation = "spegni_il_cervello"

  // MARK: - Initialization
  init() {
    // 依赖注入将在 onAppear 中设置
  }

  // MARK: - Public Methods
  func onAppear() {
    setupDependencies()
    loadAvailableAnimations()

    // 自动播放默认动画
    Task {
      await playDefaultAnimation()
    }
  }

  func playDefaultAnimation() {
    playAnimation(defaultAnimation)
  }

  func playAnimation(_ name: String) {
    Task {
      await animationService?.playAnimation(named: name)
      await updateState()

      // 记录用户交互
      loggingService?.logUserInteraction("播放动画: \(name)")
    }
  }

  func pauseAnimation() {
    Task {
      await animationService?.pauseAnimation()
      await updateState()
    }
  }

  func resumeAnimation() {
    Task {
      await animationService?.resumeAnimation()
      await updateState()
    }
  }

  func stopAnimation() {
    Task {
      await animationService?.stopAnimation()
      await updateState()
    }
  }

  // MARK: - Private Methods
  private func setupDependencies() {
    let container = DIContainer.shared
    self.animationService = container.animationService
    self.loggingService = container.loggingService

    loggingService?.logInfo("🎬 AnimationViewModel 初始化完成")
  }

  private func loadAvailableAnimations() {
    // 这里可以从动画服务加载可用的动画列表
    availableAnimations = [defaultAnimation]
  }

  private func updateState() async {
    guard let animationService = animationService else { return }

    await MainActor.run {
      self.isAnimationPlaying = animationService.isAnimationPlaying
      self.currentAnimation = animationService.currentAnimation
    }
  }
}
