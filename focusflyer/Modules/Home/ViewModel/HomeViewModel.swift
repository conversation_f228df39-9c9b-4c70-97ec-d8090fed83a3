//
//  HomeViewModel.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Foundation
import SwiftUI

/// 主页面视图模型
@MainActor
final class HomeViewModel: ObservableObject {

  // MARK: - Published Properties
  @Published var isLoading = false
  @Published var errorMessage: String?

  // MARK: - Dependencies
  private var gameEngineService: (any GameEngineServiceProtocol)?
  private var loggingService: LoggingServiceProtocol?

  // MARK: - Initialization
  init() {
    // 依赖注入将在 onAppear 中设置
  }

  // MARK: - Public Methods
  func onAppear() {
    // 设置依赖注入
    setupDependencies()

    // 初始化页面
    initializePage()
  }

  // MARK: - Private Methods
  private func setupDependencies() {
    let container = DIContainer.shared
    self.gameEngineService = container.gameEngineService
    self.loggingService = container.loggingService

    loggingService?.logInfo("🏠 HomeViewModel 初始化完成")
  }

  private func initializePage() {
    isLoading = true

    Task {
      // 确保游戏引擎已初始化
      await gameEngineService?.initializeEngine()

      await MainActor.run {
        isLoading = false
      }
    }
  }

  func handleError(_ error: Error) {
    loggingService?.logError("主页面错误: \(error.localizedDescription)")
    errorMessage = error.localizedDescription
  }
}
