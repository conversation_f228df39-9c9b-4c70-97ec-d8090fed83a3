//
//  HomeView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import RiveRuntime
import SwiftGodot
import SwiftGodotKit
import SwiftUI

/// 主视图 - 业界最佳实践架构
/// Godot引擎作为底层渲染基础，所有SwiftUI视图悬浮在上面
struct HomeView: View {
  @StateObject private var viewModel = HomeViewModel()
  @EnvironmentObject private var diContainer: DIContainer
  @State private var showProjectStatus = false
  @State private var showWelcomeOverlay = true

  var body: some View {
    ZStack {
      // 底层：Godot引擎渲染 - 始终存在
      GameBackgroundView()

      // 悬浮层：SwiftUI界面组件
      overlayViews
    }
    .ignoresSafeArea(.all)  // 全屏体验
    .onAppear {
      viewModel.onAppear()
    }
    .onReceive(NotificationCenter.default.publisher(for: .toggleProjectStatus)) { _ in
      guard diContainer.configurationService.enableDebugPanel else { return }
      
      diContainer.loggingService.logUserInteraction("调试面板 - 切换状态面板")
      withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
        showProjectStatus.toggle()
      }
    }
  }

  @ViewBuilder
  private var overlayViews: some View {
    ZStack {
      // 左上角：悬浮控制面板 - 层级1
      DebugFloatingPanel()
        .zIndex(1)

      // 右下角：动画悬浮组件 - 层级2（更高优先级，确保拖拽正常）
      AnimationFloatingView()
        .zIndex(2)

      // 顶部中央：项目状态组件（仅开发模式）- 层级10（最高层级）  
      if diContainer.configurationService.enableDebugPanel && showProjectStatus {
        GeometryReader { geometry in
          VStack {
            // 状态面板居中显示在灵动岛下方，使用安全位置计算
            statusPanelView(geometry: geometry)
            
            Spacer()
          }
        }
        .zIndex(10)  // 设为最高层级，确保不被其他UI元素遮挡
        .allowsHitTesting(true)  // 允许交互，因为状态面板需要点击
      }

      // 中央：欢迎信息（在引擎就绪后3秒自动消失）
      if case .ready = diContainer.gameEngineService.engineState, showWelcomeOverlay {
        welcomeOverlay
          .zIndex(3)  // 最高层级，显示在所有内容之上
          .onAppear {
            // 3秒后自动隐藏欢迎信息
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
              withAnimation(.easeOut(duration: 1.0)) {
                showWelcomeOverlay = false
              }
            }
          }
      }
    }
  }

  @ViewBuilder
  private var welcomeOverlay: some View {
    VStack {
      Spacer()

      HStack {
        Spacer()

        VStack(spacing: Spacing.md) {
          HStack {
            VStack(alignment: .leading, spacing: Spacing.sm) {
              Text("🎮 FocusFlyer")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)

              Text("SwiftUI + Godot + Rive")
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
            }

            Spacer()

            Button(action: {
              withAnimation(.easeOut(duration: 0.5)) {
                showWelcomeOverlay = false
              }
            }) {
              Image(systemName: "xmark.circle.fill")
                .font(.title3)
                .foregroundColor(.white.opacity(0.7))
            }
            .buttonStyle(PlainButtonStyle())
          }
        }
        .padding(Spacing.Container.padding)
        .background(.ultraThinMaterial)
        .cornerRadius(Spacing.Container.cornerRadius)
        .shadow(radius: Spacing.sm)
        .transition(
          .asymmetric(
            insertion: .scale.combined(with: .opacity),
            removal: .scale.combined(with: .opacity)
          )
        )

        Spacer()
      }

      Spacer()
    }
    .allowsHitTesting(true)  // 允许点击关闭按钮
  }
  
  @ViewBuilder
  private func statusPanelView(geometry: GeometryProxy) -> some View {
    let safeTopPosition = SafeAreaHelper.calculateStatusPanelSafeTopPosition(
      in: geometry,
      panelHeight: 400  // 估算展开后的高度
    )
    
    // 确保面板不会超出底部安全区域
    let maxAvailableHeight = geometry.size.height - safeTopPosition - geometry.safeAreaInsets.bottom - SafeAreaHelper.deviceSpecificMargins.bottom
    
    DebugStatusView()
      .transition(
        .asymmetric(
          insertion: .move(edge: .top).combined(with: .opacity),
          removal: .move(edge: .top).combined(with: .opacity)
        ))
      .padding(.top, safeTopPosition)
      .padding(.horizontal, Spacing.md)
      .frame(maxWidth: .infinity, maxHeight: maxAvailableHeight, alignment: .center)
      .clipped()  // 防止内容超出边界
  }
}

#Preview {
  HomeView()
    .environmentObject(DIContainer.shared)
}
