//
//  GameBackgroundView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Combine
import SwiftGodot
import SwiftGodotKit
import SwiftUI

/// 游戏背景视图 - Godot引擎渲染
struct GameBackgroundView: View {
  @StateObject private var viewModel = GameViewModel()
  @EnvironmentObject private var diContainer: DIContainer
  @State private var currentEngineState: GameEngineState = .initializing

  var body: some View {
    ZStack {
      // 根据引擎状态显示不同内容
      switch currentEngineState {
      case .initializing:
        initializingView
      case .ready:
        godotGameView
      case .error(let message):
        errorView(message)
      }
    }
    .onAppear {
      viewModel.onAppear()
      currentEngineState = diContainer.gameEngineService.engineState
    }
    .onReceive(diContainer.gameEngineService.$engineState) { newState in
      currentEngineState = newState
    }
  }

  @ViewBuilder
  private var initializingView: some View {
    Rectangle()
      .fill(
        LinearGradient(
          gradient: Gradient(colors: [.black, .gray.opacity(0.3)]),
          startPoint: .top,
          endPoint: .bottom
        )
      )
      .overlay(
        VStack(spacing: Spacing.lg) {
          ProgressView()
            .scaleEffect(1.5)
            .tint(.white)

          Text("初始化 Godot 引擎...")
            .font(.headline)
            .foregroundColor(.white)
        }
      )
  }

  @ViewBuilder
  private var godotGameView: some View {
    if let godotApp = diContainer.gameEngineService.godotApp {
      GodotAppView()
        .environment(\.godotApp, godotApp)
        .ignoresSafeArea(.all)
        .allowsHitTesting(false)  // 让 SwiftUI 悬浮层处理交互
    } else {
      initializingView
    }
  }

  @ViewBuilder
  private func errorView(_ message: String) -> some View {
    Rectangle()
      .fill(Color.red.opacity(0.3))
      .overlay(
        VStack(spacing: Spacing.lg) {
          Image(systemName: "exclamationmark.triangle")
            .font(.system(size: 48))
            .foregroundColor(.white)

          Text("引擎初始化失败")
            .font(.headline)
            .foregroundColor(.white)

          Text(message)
            .font(.caption)
            .foregroundColor(.white.opacity(0.8))
            .multilineTextAlignment(.center)
            .padding(.horizontal)

          Button("重试") {
            Task {
              await diContainer.gameEngineService.reinitializeEngine()
            }
          }
          .buttonStyle(.borderedProminent)
        }
      )
  }
}
