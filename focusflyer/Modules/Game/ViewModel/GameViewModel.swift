//  GameViewModel.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Foundation
import SwiftUI

/// 游戏模块视图模型
@MainActor
final class GameViewModel: ObservableObject {

  // MARK: - Published Properties
  @Published var currentScene: String = "main"
  @Published var isEngineReady: Bool = false
  @Published var errorMessage: String?

  // MARK: - Dependencies
  private var gameEngineService: (any GameEngineServiceProtocol)?
  private var loggingService: LoggingServiceProtocol?

  // MARK: - Initialization
  init() {
    // 依赖注入将在 onAppear 中设置
  }

  // MARK: - Public Methods
  func onAppear() {
    setupDependencies()
    monitorEngineState()
  }

  func switchScene(to sceneName: String) {
    Task {
      await gameEngineService?.switchScene(to: sceneName)
      await MainActor.run {
        self.currentScene = sceneName
      }
    }
  }

  func restartEngine() {
    Task {
      await gameEngineService?.reinitializeEngine()
    }
  }

  // MARK: - Private Methods
  private func setupDependencies() {
    let container = DIContainer.shared
    self.gameEngineService = container.gameEngineService
    self.loggingService = container.loggingService

    loggingService?.logInfo("🎮 GameViewModel 初始化完成")
  }

  private func monitorEngineState() {
    // 监控引擎状态变化
    guard let gameEngineService = gameEngineService else { return }

    // 这里可以添加 Combine 监听器来观察引擎状态变化
    // 由于 GameEngineService 实现了 ObservableObject，我们可以观察它的状态

    isEngineReady = gameEngineService.engineState == .ready
  }
}
