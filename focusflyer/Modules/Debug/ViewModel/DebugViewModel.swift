//
//  DebugViewModel.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Foundation
import SwiftUI

/// 调试模块视图模型
@MainActor
final class DebugViewModel: ObservableObject {

  // MARK: - Published Properties
  @Published var engineStatus: String = "未知"
  @Published var engineStatusColor: Color = .gray
  @Published var animationStatus: String = "未知"
  @Published var animationStatusColor: Color = .gray
  @Published var logEntries: [String] = []

  // MARK: - Dependencies
  private var gameEngineService: (any GameEngineServiceProtocol)?
  private var animationService: (any AnimationServiceProtocol)?
  private var loggingService: LoggingServiceProtocol?
  private var configurationService: ConfigurationServiceProtocol?

  // MARK: - Initialization
  init() {
    // 依赖注入将在 onAppear 中设置
  }

  // MARK: - Public Methods
  func onAppear() {
    setupDependencies()
    startStatusMonitoring()
  }

  func switchScene(_ sceneName: String) {
    Task {
      await gameEngineService?.switchScene(to: sceneName)
      loggingService?.logUserInteraction("切换场景: \(sceneName)")
    }
  }

  func restartEngine() {
    Task {
      await gameEngineService?.reinitializeEngine()
      loggingService?.logUserInteraction("重启引擎")
    }
  }

  func clearLogs() {
    loggingService?.clearLogs()
    logEntries.removeAll()
    loggingService?.logUserInteraction("清理日志")
  }

  func generateSystemReport() -> String {
    var report = "=== 系统状态报告 ===\n\n"

    // 引擎状态
    report += "🎮 引擎状态: \(engineStatus)\n"

    // 动画状态
    report += "🎨 动画状态: \(animationStatus)\n"

    // 配置信息
    if let config = configurationService {
      report += "🔧 调试模式: \(config.isDebugBuild ? "开启" : "关闭")\n"
      report += "📱 应用版本: \(config.getAppVersion())\n"
      report += "🏗️ 构建版本: \(config.getBuildNumber())\n"
    }

    // 最近日志
    report += "\n📝 最近日志:\n"
    for entry in logEntries.suffix(5) {
      report += "  • \(entry)\n"
    }

    return report
  }

  // MARK: - Private Methods
  private func setupDependencies() {
    let container = DIContainer.shared
    self.gameEngineService = container.gameEngineService
    self.animationService = container.animationService
    self.loggingService = container.loggingService
    self.configurationService = container.configurationService

    loggingService?.logInfo("🐛 DebugViewModel 初始化完成")
  }

  private func startStatusMonitoring() {
    // 启动定时器监控状态
    Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
      self?.updateStatus()
    }

    // 立即更新一次
    updateStatus()
  }

  private func updateStatus() {
    updateEngineStatus()
    updateAnimationStatus()
  }

  private func updateEngineStatus() {
    guard let gameEngineService = gameEngineService else { return }

    switch gameEngineService.engineState {
    case .initializing:
      engineStatus = "初始化"
      engineStatusColor = .orange
    case .ready:
      engineStatus = "就绪"
      engineStatusColor = .green
    case .error(_):
      engineStatus = "错误"
      engineStatusColor = .red
    }
  }

  private func updateAnimationStatus() {
    guard let animationService = animationService else { return }

    if animationService.isAnimationPlaying {
      animationStatus = "播放中"
      animationStatusColor = .green
    } else {
      animationStatus = "停止"
      animationStatusColor = .gray
    }
  }
}
