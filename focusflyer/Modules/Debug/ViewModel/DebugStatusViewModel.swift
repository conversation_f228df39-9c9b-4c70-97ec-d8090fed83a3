//
//  DebugStatusViewModel.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Foundation
import SwiftUI

/// 调试状态视图模型
@MainActor
final class DebugStatusViewModel: ObservableObject {

  // MARK: - Published Properties
  @Published var appVersion: String = ""
  @Published var buildNumber: String = ""
  @Published var buildMode: String = ""
  @Published var isDebugBuild: Bool = false

  @Published var engineStatus: String = "未知"
  @Published var engineStatusColor: Color = .gray
  @Published var currentScene: String?

  @Published var animationStatus: String = "未知"
  @Published var animationStatusColor: Color = .gray
  @Published var currentAnimation: String?

  @Published var pckStatus: String = "未知"
  @Published var pckStatusColor: Color = .gray
  @Published var riveStatus: String = "未知"
  @Published var riveStatusColor: Color = .gray

  @Published var systemReport: String = ""
  @Published var showReport: Bool = false

  // MARK: - Dependencies
  private var gameEngineService: (any GameEngineServiceProtocol)?
  private var animationService: (any AnimationServiceProtocol)?
  private var loggingService: LoggingServiceProtocol?
  private var configurationService: ConfigurationServiceProtocol?

  // MARK: - Initialization
  init() {
    // 依赖注入将在 onAppear 中设置
  }

  // MARK: - Public Methods
  func onAppear() {
    setupDependencies()
    loadAppInfo()
    startStatusMonitoring()
  }

  func generateReport() {
    systemReport = createSystemReport()
    showReport = true
    loggingService?.logUserInteraction("生成系统报告")
  }

  func clearLogs() {
    loggingService?.clearLogs()
    loggingService?.logUserInteraction("清理日志")
  }

  // MARK: - Private Methods
  private func setupDependencies() {
    let container = DIContainer.shared
    self.gameEngineService = container.gameEngineService
    self.animationService = container.animationService
    self.loggingService = container.loggingService
    self.configurationService = container.configurationService

    loggingService?.logInfo("📊 DebugStatusViewModel 初始化完成")
  }

  private func loadAppInfo() {
    guard let config = configurationService else { return }

    appVersion = config.getAppVersion()
    buildNumber = config.getBuildNumber()
    isDebugBuild = config.isDebugBuild
    buildMode = isDebugBuild ? "Debug" : "Release"
  }

  private func startStatusMonitoring() {
    // 启动定时器监控状态
    Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] _ in
      Task { @MainActor in
        self?.updateAllStatus()
      }
    }

    // 立即更新一次
    Task { @MainActor in
      updateAllStatus()
    }
  }

  private func updateAllStatus() {
    updateEngineStatus()
    updateAnimationStatus()
    updateResourceStatus()
  }

  private func updateEngineStatus() {
    guard let gameEngineService = gameEngineService else { return }

    switch gameEngineService.engineState {
    case .initializing:
      engineStatus = "初始化中"
      engineStatusColor = .orange
    case .ready:
      engineStatus = "就绪"
      engineStatusColor = .green
      currentScene = gameEngineService.currentScene
    case .error(_):
      engineStatus = "错误"
      engineStatusColor = .red
    }
  }

  private func updateAnimationStatus() {
    guard let animationService = animationService else { return }

    if animationService.isAnimationPlaying {
      animationStatus = "播放中"
      animationStatusColor = .green
      currentAnimation = animationService.currentAnimation
    } else {
      animationStatus = "停止"
      animationStatusColor = .gray
      currentAnimation = nil
    }
  }

  private func updateResourceStatus() {
    // 检查PCK文件状态
    if Bundle.main.path(forResource: "main", ofType: "pck") != nil {
      pckStatus = "正常"
      pckStatusColor = .green
    } else {
      pckStatus = "缺失"
      pckStatusColor = .red
    }

    // 检查Rive文件状态
    if Bundle.main.path(forResource: "spegni_il_cervello", ofType: "riv") != nil {
      riveStatus = "正常"
      riveStatusColor = .green
    } else {
      riveStatus = "缺失"
      riveStatusColor = .red
    }
  }

  private func createSystemReport() -> String {
    var report = "=== FocusFlyer 系统状态报告 ===\n\n"

    // 应用信息
    report += "📱 应用信息:\n"
    report += "  版本: \(appVersion)\n"
    report += "  构建: \(buildNumber)\n"
    report += "  模式: \(buildMode)\n\n"

    // 引擎状态
    report += "🎮 引擎状态:\n"
    report += "  状态: \(engineStatus)\n"
    if let scene = currentScene {
      report += "  当前场景: \(scene)\n"
    }
    report += "\n"

    // 动画状态
    report += "🎨 动画状态:\n"
    report += "  状态: \(animationStatus)\n"
    if let animation = currentAnimation {
      report += "  当前动画: \(animation)\n"
    }
    report += "\n"

    // 资源状态
    report += "📦 资源状态:\n"
    report += "  PCK文件: \(pckStatus)\n"
    report += "  Rive文件: \(riveStatus)\n\n"

    // 设备信息
    report += "📱 设备信息:\n"
    report += "  系统: iOS \(UIDevice.current.systemVersion)\n"
    report += "  设备: \(UIDevice.current.model)\n"

    // 时间戳
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    formatter.timeStyle = .medium
    report += "\n生成时间: \(formatter.string(from: Date()))"

    return report
  }
}
