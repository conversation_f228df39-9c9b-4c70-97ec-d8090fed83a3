//
//  DebugStatusView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import SwiftUI

/// 调试状态视图 - 详细的系统状态信息
struct DebugStatusView: View {
  @StateObject private var viewModel = DebugStatusViewModel()
  @EnvironmentObject private var diContainer: DIContainer
  @State private var isExpanded = true  // 默认展开状态

  var body: some View {
    statusPanel
      .onAppear {
        viewModel.onAppear()
      }
  }

  @ViewBuilder
  private var statusPanel: some View {
    VStack(spacing: Spacing.md) {
      // 标题栏
      headerView

      if isExpanded {
        // 详细状态信息
        detailView
      }
    }
    .padding(Spacing.md)
    .background(.regularMaterial)
    .cornerRadius(Spacing.Container.cornerRadius)
    .shadow(radius: 20)
    .frame(maxWidth: 300)
    .fixedSize(horizontal: false, vertical: true)  // 允许垂直自适应高度
    .animation(.spring(response: 0.5, dampingFraction: 0.7), value: isExpanded)
  }

  @ViewBuilder
  private var headerView: some View {
    HStack {
      Text("📊 系统状态")
        .font(.headline)
        .foregroundColor(.primary)

      Spacer()

      // 展开/收缩按钮
      Button(action: {
        withAnimation(FloatingPositionHelper.expandAnimation) {
          isExpanded.toggle()
        }
      }) {
        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
          .font(.caption)
          .foregroundColor(.secondary)
          .frame(width: 44, height: 44)  // 增加触摸区域
          .contentShape(Rectangle())  // 确保整个区域都可点击
      }
      .buttonStyle(PlainButtonStyle())

      // 关闭按钮
      Button(action: {
        // 发送通知关闭状态面板
        NotificationCenter.default.post(name: .toggleProjectStatus, object: nil)
      }) {
        Image(systemName: "xmark.circle.fill")
          .font(.title3)
          .foregroundColor(.secondary)
          .frame(width: 44, height: 44)  // 增加触摸区域
          .contentShape(Rectangle())  // 确保整个区域都可点击
      }
      .buttonStyle(PlainButtonStyle())
    }
  }

  @ViewBuilder
  private var detailView: some View {
    VStack(spacing: Spacing.md) {
      // 系统概览
      systemOverview

      Divider()

      // 引擎状态
      engineStatus

      Divider()

      // 动画状态
      animationStatus

      Divider()

      // 资源状态
      resourceStatus

      Divider()

      // 操作按钮
      actionButtons
    }
  }

  @ViewBuilder
  private var systemOverview: some View {
    VStack(alignment: .leading, spacing: Spacing.sm) {
      Text("🏗️ 系统概览")
        .font(.subheadline)
        .fontWeight(.semibold)

      HStack {
        Text("版本:")
        Spacer()
        Text(viewModel.appVersion)
          .foregroundColor(.secondary)
      }
      .font(.caption)

      HStack {
        Text("构建:")
        Spacer()
        Text(viewModel.buildNumber)
          .foregroundColor(.secondary)
      }
      .font(.caption)

      HStack {
        Text("模式:")
        Spacer()
        Text(viewModel.buildMode)
          .foregroundColor(viewModel.isDebugBuild ? .orange : .green)
      }
      .font(.caption)
    }
  }

  @ViewBuilder
  private var engineStatus: some View {
    VStack(alignment: .leading, spacing: Spacing.sm) {
      Text("🎮 引擎状态")
        .font(.subheadline)
        .fontWeight(.semibold)

      HStack {
        Circle()
          .fill(viewModel.engineStatusColor)
          .frame(width: 8, height: 8)

        Text(viewModel.engineStatus)
          .font(.caption)

        Spacer()
      }

      if let currentScene = viewModel.currentScene {
        HStack {
          Text("当前场景:")
          Spacer()
          Text(currentScene)
            .foregroundColor(.secondary)
        }
        .font(.caption)
      }
    }
  }

  @ViewBuilder
  private var animationStatus: some View {
    VStack(alignment: .leading, spacing: Spacing.sm) {
      Text("🎨 动画状态")
        .font(.subheadline)
        .fontWeight(.semibold)

      HStack {
        Circle()
          .fill(viewModel.animationStatusColor)
          .frame(width: 8, height: 8)

        Text(viewModel.animationStatus)
          .font(.caption)

        Spacer()
      }

      if let currentAnimation = viewModel.currentAnimation {
        HStack {
          Text("当前动画:")
          Spacer()
          Text(currentAnimation)
            .foregroundColor(.secondary)
        }
        .font(.caption)
      }
    }
  }

  @ViewBuilder
  private var resourceStatus: some View {
    VStack(alignment: .leading, spacing: Spacing.sm) {
      Text("📦 资源状态")
        .font(.subheadline)
        .fontWeight(.semibold)

      HStack {
        Text("PCK文件:")
        Spacer()
        Text(viewModel.pckStatus)
          .foregroundColor(viewModel.pckStatusColor)
      }
      .font(.caption)

      HStack {
        Text("Rive文件:")
        Spacer()
        Text(viewModel.riveStatus)
          .foregroundColor(viewModel.riveStatusColor)
      }
      .font(.caption)
    }
  }

  @ViewBuilder
  private var actionButtons: some View {
    VStack(spacing: Spacing.sm) {
      HStack(spacing: Spacing.sm) {
        Button("生成报告") {
          viewModel.generateReport()
        }
        .buttonStyle(.bordered)
        .controlSize(.small)

        Button("清理日志") {
          viewModel.clearLogs()
        }
        .buttonStyle(.bordered)
        .controlSize(.small)
      }

      if viewModel.showReport {
        ScrollView {
          Text(viewModel.systemReport)
            .font(.caption)
            .foregroundColor(.secondary)
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .frame(height: 100)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
      }
    }
  }
}
