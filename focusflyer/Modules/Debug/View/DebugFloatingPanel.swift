//  DebugFloatingPanel.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import SwiftUI

extension Notification.Name {
  static let toggleProjectStatus = Notification.Name("toggleProjectStatus")
}

/// 调试悬浮面板 - 开发和调试工具
struct DebugFloatingPanel: View {
  @StateObject private var viewModel = DebugViewModel()
  @EnvironmentObject private var diContainer: DIContainer
  @StateObject private var panelManager = FloatingPanelManager()
  
  // 估算的面板大小
  private let estimatedPanelSize = CGSize(width: 220, height: 280)

  var body: some View {
    GeometryReader { geometry in
      ZStack {
        // 悬浮控制按钮
        floatingPanel
          .position(panelManager.position)
          .floatingDragGesture(
            manager: panelManager,
            estimatedPanelSize: estimatedPanelSize
          )
      }
      .onAppear {
        viewModel.onAppear()
        panelManager.setInitialPosition(
          preference: .topLeft,
          buttonSize: 60,
          geometry: geometry
        )
      }
    }
  }

  @ViewBuilder
  private var floatingPanel: some View {
    VStack {
      if panelManager.isExpanded {
        expandedView
      } else {
        collapsedView
      }
    }
    .animation(FloatingPositionHelper.expandAnimation, value: panelManager.isExpanded)
  }

  @ViewBuilder
  private var collapsedView: some View {
    Button(action: {
      // 只有在未拖拽时才响应点击
      if !panelManager.dragState.shouldPreventClick {
        withAnimation(FloatingPositionHelper.expandAnimation) {
          panelManager.toggleExpanded(estimatedPanelSize: estimatedPanelSize)
        }
      }
    }) {
      ZStack {
        Circle()
          .fill(.ultraThinMaterial)
          .frame(width: 60, height: 60)
          .shadow(radius: 10)

        Image(systemName: "gear")
          .font(.title2)
          .foregroundColor(.white)
      }
    }
    .buttonStyle(PlainButtonStyle())
  }

  @ViewBuilder
  private var expandedView: some View {
    VStack(spacing: Spacing.md) {
      // 调试控制面板
      controlPanel

      // 折叠按钮
      Button(action: {
        // 只有在未拖拽时才响应点击
        if !panelManager.dragState.shouldPreventClick {
          withAnimation(FloatingPositionHelper.expandAnimation) {
            panelManager.toggleExpanded(estimatedPanelSize: estimatedPanelSize)
          }
        }
      }) {
        ZStack {
          Circle()
            .fill(.ultraThinMaterial)
            .frame(width: 60, height: 60)
            .shadow(radius: 10)

          Image(systemName: "chevron.up")
            .font(.title2)
            .foregroundColor(.white)
        }
      }
      .buttonStyle(PlainButtonStyle())
    }
    .padding(Spacing.md)
    .background(.regularMaterial)
    .cornerRadius(Spacing.Container.cornerRadius)
    .shadow(radius: 20)
    .frame(maxWidth: panelLayout?.size.width ?? 220)
    .offset(x: panelLayout?.offset.x ?? 0, y: panelLayout?.offset.y ?? 0)
  }
  
  // 计算面板布局
  private var panelLayout: (position: CGPoint, size: CGSize, offset: CGPoint)? {
    panelManager.getPanelLayout(estimatedSize: estimatedPanelSize)
  }

  @ViewBuilder
  private var controlPanel: some View {
    VStack(spacing: Spacing.sm) {
      Text("🔧 开发工具")
        .font(.headline)
        .foregroundColor(.primary)

      // 引擎控制按钮
      VStack(spacing: Spacing.sm) {
        // 场景切换
        HStack(spacing: Spacing.sm) {
          Button("场景1") {
            viewModel.switchScene("scene1")
          }
          .buttonStyle(.bordered)

          Button("场景2") {
            viewModel.switchScene("scene2")
          }
          .buttonStyle(.bordered)
        }

        // 引擎控制
        HStack(spacing: Spacing.sm) {
          Button("重启引擎") {
            viewModel.restartEngine()
          }
          .buttonStyle(.borderedProminent)

          Button("清理日志") {
            viewModel.clearLogs()
          }
          .buttonStyle(.bordered)
        }
        
        // 调试状态切换按钮（替代三击手势）
        if diContainer.configurationService.enableDebugPanel {
          Button("状态面板") {
            // 通知父视图切换状态面板显示
            NotificationCenter.default.post(name: .toggleProjectStatus, object: nil)
          }
          .buttonStyle(.bordered)
          .font(.caption)
        }
      }

      // 状态指示器
      HStack {
        statusIndicator(
          title: "引擎",
          status: viewModel.engineStatus,
          color: viewModel.engineStatusColor
        )

        Spacer()

        statusIndicator(
          title: "动画",
          status: viewModel.animationStatus,
          color: viewModel.animationStatusColor
        )
      }
    }
  }

  @ViewBuilder
  private func statusIndicator(title: String, status: String, color: Color) -> some View {
    VStack(spacing: 2) {
      Circle()
        .fill(color)
        .frame(width: 8, height: 8)

      Text(title)
        .font(.caption2)
        .foregroundColor(.secondary)

      Text(status)
        .font(.caption2)
        .foregroundColor(.primary)
    }
  }
}