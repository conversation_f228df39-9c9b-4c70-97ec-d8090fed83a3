import SwiftUI

/// Design system spacing constants and layout guidelines
public enum Spacing {

  // MARK: - Base Spacing Units
  /// Base spacing unit (4pt) - all spacing should be multiples of this
  public static let base: CGFloat = 4

  // MARK: - Standard Spacing Values
  /// Extra small spacing (4pt)
  public static let xs: CGFloat = base * 1  // 4pt

  /// Small spacing (8pt)
  public static let sm: CGFloat = base * 2  // 8pt

  /// Medium spacing (12pt)
  public static let md: CGFloat = base * 3  // 12pt

  /// Large spacing (16pt)
  public static let lg: CGFloat = base * 4  // 16pt

  /// Extra large spacing (24pt)
  public static let xl: CGFloat = base * 6  // 24pt

  /// 2x extra large spacing (32pt)
  public static let xxl: CGFloat = base * 8  // 32pt

  /// 3x extra large spacing (48pt)
  public static let xxxl: CGFloat = base * 12  // 48pt

  // MARK: - Component Specific Spacing

  /// Button internal padding
  public enum Button {
    /// Small button padding (8pt vertical, 12pt horizontal)
    public static let small = EdgeInsets(top: sm, leading: md, bottom: sm, trailing: md)

    /// Medium button padding (12pt vertical, 16pt horizontal)
    public static let medium = EdgeInsets(top: md, leading: lg, bottom: md, trailing: lg)

    /// Large button padding (16pt vertical, 24pt horizontal)
    public static let large = EdgeInsets(top: lg, leading: xl, bottom: lg, trailing: xl)

    /// Minimum touch target size (44pt)
    public static let minTouchTarget: CGFloat = 44
  }

  /// Card and container spacing
  public enum Container {
    /// Card internal padding
    public static let padding = EdgeInsets(top: lg, leading: lg, bottom: lg, trailing: lg)

    /// Card corner radius
    public static let cornerRadius: CGFloat = md

    /// Large corner radius for prominent containers
    public static let largeCornerRadius: CGFloat = lg

    /// Card spacing between cards
    public static let spacing: CGFloat = lg

    /// Section spacing
    public static let sectionSpacing: CGFloat = xl
  }

  /// Screen and layout spacing
  public enum Screen {
    /// Screen edge padding
    public static let padding = EdgeInsets(top: lg, leading: lg, bottom: lg, trailing: lg)

    /// Safe area additional padding
    public static let safeAreaPadding: CGFloat = lg

    /// Navigation bar height
    public static let navigationBarHeight: CGFloat = 44

    /// Tab bar height
    public static let tabBarHeight: CGFloat = 49

    /// Status bar height (standard)
    public static let statusBarHeight: CGFloat = 20

    /// Bottom safe area (for home indicator)
    public static let bottomSafeArea: CGFloat = 34
  }

  /// Floating panel and overlay spacing
  public enum FloatingPanel {
    /// Panel edge margin from screen edges
    public static let edgeMargin: CGFloat = lg

    /// Panel internal padding
    public static let padding = EdgeInsets(top: lg, leading: lg, bottom: lg, trailing: lg)

    /// Panel corner radius
    public static let cornerRadius: CGFloat = xl

    /// Panel shadow offset
    public static let shadowOffset = CGSize(width: 0, height: sm)

    /// Panel shadow radius
    public static let shadowRadius: CGFloat = lg

    /// Panel backdrop blur radius
    public static let backdropBlur: CGFloat = 20
  }

  /// Game view specific spacing
  public enum Game {
    /// Game container padding
    public static let containerPadding: CGFloat = 0  // Full screen

    /// Game overlay margin
    public static let overlayMargin: CGFloat = lg

    /// Control spacing
    public static let controlSpacing: CGFloat = md

    /// HUD element margin
    public static let hudMargin: CGFloat = sm
  }

  // MARK: - Grid System

  /// Grid system for consistent layouts
  public enum Grid {
    /// Column count for different screen sizes
    public static let phoneColumns: Int = 2
    public static let tabletColumns: Int = 3
    public static let desktopColumns: Int = 4

    /// Grid gutter (space between columns)
    public static let gutter: CGFloat = lg

    /// Grid margin (space from screen edges)
    public static let margin: CGFloat = lg
  }

  // MARK: - Animation Constants

  /// Animation timing and easing
  public enum Animation {
    /// Quick animations (0.2s)
    public static let quick: TimeInterval = 0.2

    /// Standard animations (0.3s)
    public static let standard: TimeInterval = 0.3

    /// Slow animations (0.5s)
    public static let slow: TimeInterval = 0.5

    /// Spring animation response
    public static let springResponse: Double = 0.6

    /// Spring animation damping
    public static let springDamping: Double = 0.8

    /// Standard easing curve
    public static let easing = SwiftUI.Animation.easeInOut(duration: standard)

    /// Quick easing curve
    public static let quickEasing = SwiftUI.Animation.easeInOut(duration: quick)

    /// Slow easing curve
    public static let slowEasing = SwiftUI.Animation.easeInOut(duration: slow)

    /// Spring animation
    public static let spring = SwiftUI.Animation.spring(
      response: springResponse, dampingFraction: springDamping)

    /// Interactive spring for gestures
    public static let interactiveSpring = SwiftUI.Animation.spring(
      response: 0.4, dampingFraction: 0.8)
  }

  // MARK: - Breakpoints

  /// Screen size breakpoints for responsive design
  public enum Breakpoint {
    /// Small phone screens (< 375pt)
    public static let small: CGFloat = 375

    /// Medium phone screens (375-414pt)
    public static let medium: CGFloat = 414

    /// Large phone screens (414-768pt)
    public static let large: CGFloat = 768

    /// Tablet screens (768-1024pt)
    public static let tablet: CGFloat = 1024

    /// Desktop screens (> 1024pt)
    public static let desktop: CGFloat = 1024
  }

  // MARK: - Helper Methods

  /// Get responsive spacing based on screen width
  public static func responsive(compact: CGFloat, regular: CGFloat, screenWidth: CGFloat) -> CGFloat
  {
    return screenWidth < Breakpoint.tablet ? compact : regular
  }

  /// Get grid column width for given screen width
  public static func columnWidth(screenWidth: CGFloat, columns: Int) -> CGFloat {
    let totalGutter = CGFloat(columns - 1) * Grid.gutter
    let totalMargin = Grid.margin * 2
    let availableWidth = screenWidth - totalGutter - totalMargin
    return availableWidth / CGFloat(columns)
  }

  /// Create consistent EdgeInsets
  public static func insets(
    top: CGFloat = 0,
    leading: CGFloat = 0,
    bottom: CGFloat = 0,
    trailing: CGFloat = 0
  ) -> EdgeInsets {
    return EdgeInsets(top: top, leading: leading, bottom: bottom, trailing: trailing)
  }

  /// Create uniform EdgeInsets
  public static func insets(all: CGFloat) -> EdgeInsets {
    return EdgeInsets(top: all, leading: all, bottom: all, trailing: all)
  }

  /// Create horizontal EdgeInsets
  public static func horizontalInsets(_ value: CGFloat) -> EdgeInsets {
    return EdgeInsets(top: 0, leading: value, bottom: 0, trailing: value)
  }

  /// Create vertical EdgeInsets
  public static func verticalInsets(_ value: CGFloat) -> EdgeInsets {
    return EdgeInsets(top: value, leading: 0, bottom: value, trailing: 0)
  }
}

// MARK: - SwiftUI Extensions

extension View {
  /// Apply standard screen padding
  public func screenPadding() -> some View {
    self.padding(Spacing.Screen.padding)
  }

  /// Apply container padding
  public func containerPadding() -> some View {
    self.padding(Spacing.Container.padding)
  }

  /// Apply floating panel styling
  public func floatingPanel() -> some View {
    self
      .padding(Spacing.FloatingPanel.padding)
      .background(.regularMaterial)
      .cornerRadius(Spacing.FloatingPanel.cornerRadius)
      .shadow(
        color: .black.opacity(0.1),
        radius: Spacing.FloatingPanel.shadowRadius,
        x: Spacing.FloatingPanel.shadowOffset.width,
        y: Spacing.FloatingPanel.shadowOffset.height
      )
  }

  /// Apply standard animation
  public func standardAnimation() -> some View {
    self.animation(Spacing.Animation.easing, value: UUID())
  }

  /// Apply quick animation
  public func quickAnimation() -> some View {
    self.animation(Spacing.Animation.quickEasing, value: UUID())
  }

  /// Apply spring animation
  public func springAnimation() -> some View {
    self.animation(Spacing.Animation.spring, value: UUID())
  }

  /// Apply responsive spacing
  public func responsiveSpacing(compact: CGFloat, regular: CGFloat) -> some View {
    GeometryReader { geometry in
      self.padding(
        Spacing.responsive(
          compact: compact,
          regular: regular,
          screenWidth: geometry.size.width
        ))
    }
  }
}

// MARK: - Accessibility

extension Spacing {
  /// Accessibility spacing adjustments
  public enum Accessibility {
    /// Additional touch target padding for accessibility
    public static let touchTargetPadding: CGFloat = sm

    /// Minimum spacing between interactive elements
    public static let minInteractiveSpacing: CGFloat = sm

    /// Focus ring padding
    public static let focusRingPadding: CGFloat = xs

    /// Screen reader navigation spacing
    public static let screenReaderSpacing: CGFloat = md
  }
}

// MARK: - Platform Specific

#if os(iOS)
  extension Spacing {
    /// iOS specific spacing constants
    public enum iOS {
      /// Navigation bar large title height
      public static let navigationBarLargeTitleHeight: CGFloat = 96

      /// Search bar height
      public static let searchBarHeight: CGFloat = 36

      /// Table view cell minimum height
      public static let tableViewCellMinHeight: CGFloat = 44

      /// Collection view cell minimum size
      public static let collectionViewCellMinSize: CGSize = CGSize(width: 44, height: 44)

      /// Keyboard toolbar height
      public static let keyboardToolbarHeight: CGFloat = 44

      /// Action sheet button height
      public static let actionSheetButtonHeight: CGFloat = 57
    }
  }
#endif

// MARK: - Debug Helpers

#if DEBUG
  extension Spacing {
    /// Debug visualization helpers
    public enum Debug {
      /// Grid overlay color
      public static let gridColor = Color.red.opacity(0.3)

      /// Spacing visualization color
      public static let spacingColor = Color.blue.opacity(0.3)

      /// Baseline grid spacing
      public static let baselineGrid: CGFloat = base
    }
  }

  extension View {
    /// Add debug grid overlay
    public func debugGrid() -> some View {
      self.overlay(
        DebugGridView()
          .allowsHitTesting(false)
      )
    }
  }

  struct DebugGridView: View {
    var body: some View {
      GeometryReader { geometry in
        let columns = Int(geometry.size.width / Spacing.Debug.baselineGrid)
        let rows = Int(geometry.size.height / Spacing.Debug.baselineGrid)

        VStack(spacing: 0) {
          ForEach(0..<rows, id: \.self) { _ in
            HStack(spacing: 0) {
              ForEach(0..<columns, id: \.self) { _ in
                Rectangle()
                  .stroke(Spacing.Debug.gridColor, lineWidth: 0.5)
                  .frame(
                    width: Spacing.Debug.baselineGrid,
                    height: Spacing.Debug.baselineGrid
                  )
              }
            }
          }
        }
      }
    }
  }
#endif
