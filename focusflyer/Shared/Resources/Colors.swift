//
//  Colors.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import SwiftUI

/// 应用颜色系统 - 统一管理所有颜色定义
extension Color {

  // MARK: - Brand Colors

  /// 主品牌色
  static let brandPrimary = Color("BrandPrimary", bundle: .main)

  /// 次要品牌色
  static let brandSecondary = Color("BrandSecondary", bundle: .main)

  /// 强调色
  static let brandAccent = Color("BrandAccent", bundle: .main)

  // MARK: - Semantic Colors

  /// 成功状态色
  static let success = Color("Success", bundle: .main)

  /// 警告状态色
  static let warning = Color("Warning", bundle: .main)

  /// 错误状态色
  static let error = Color("Error", bundle: .main)

  /// 信息状态色
  static let info = Color("Info", bundle: .main)

  // MARK: - Background Colors

  /// 主背景色
  static let backgroundPrimary = Color("BackgroundPrimary", bundle: .main)

  /// 次要背景色
  static let backgroundSecondary = Color("BackgroundSecondary", bundle: .main)

  /// 第三级背景色
  static let backgroundTertiary = Color("BackgroundTertiary", bundle: .main)

  /// 卡片背景色
  static let backgroundCard = Color("BackgroundCard", bundle: .main)

  /// 模态背景色
  static let backgroundModal = Color("BackgroundModal", bundle: .main)

  // MARK: - Text Colors

  /// 主要文本色
  static let textPrimary = Color("TextPrimary", bundle: .main)

  /// 次要文本色
  static let textSecondary = Color("TextSecondary", bundle: .main)

  /// 第三级文本色
  static let textTertiary = Color("TextTertiary", bundle: .main)

  /// 禁用文本色
  static let textDisabled = Color("TextDisabled", bundle: .main)

  /// 反色文本（用于深色背景）
  static let textInverse = Color("TextInverse", bundle: .main)

  // MARK: - Border Colors

  /// 主要边框色
  static let borderPrimary = Color("BorderPrimary", bundle: .main)

  /// 次要边框色
  static let borderSecondary = Color("BorderSecondary", bundle: .main)

  /// 分割线颜色
  static let borderDivider = Color("BorderDivider", bundle: .main)

  // MARK: - Button Colors

  /// 主要按钮背景
  static let buttonPrimaryBackground = Color("ButtonPrimaryBackground", bundle: .main)

  /// 主要按钮文本
  static let buttonPrimaryText = Color("ButtonPrimaryText", bundle: .main)

  /// 次要按钮背景
  static let buttonSecondaryBackground = Color("ButtonSecondaryBackground", bundle: .main)

  /// 次要按钮文本
  static let buttonSecondaryText = Color("ButtonSecondaryText", bundle: .main)

  /// 禁用按钮背景
  static let buttonDisabledBackground = Color("ButtonDisabledBackground", bundle: .main)

  /// 禁用按钮文本
  static let buttonDisabledText = Color("ButtonDisabledText", bundle: .main)

  // MARK: - Game Colors

  /// 游戏主题色
  static let gameTheme = Color("GameTheme", bundle: .main)

  /// 游戏背景色
  static let gameBackground = Color("GameBackground", bundle: .main)

  /// 游戏UI覆盖层
  static let gameOverlay = Color("GameOverlay", bundle: .main)

  /// 游戏HUD背景
  static let gameHUD = Color("GameHUD", bundle: .main)

  // MARK: - Debug Colors

  /// 调试面板背景
  static let debugBackground = Color("DebugBackground", bundle: .main)

  /// 调试文本色
  static let debugText = Color("DebugText", bundle: .main)

  /// 性能监控颜色
  static let debugPerformance = Color("DebugPerformance", bundle: .main)

  // MARK: - Shadow Colors

  /// 主要阴影色
  static let shadowPrimary = Color("ShadowPrimary", bundle: .main)

  /// 次要阴影色
  static let shadowSecondary = Color("ShadowSecondary", bundle: .main)
}

// MARK: - Color Extensions

extension Color {

  /// 获取颜色的十六进制字符串表示
  var hexString: String {
    let uiColor = UIColor(self)
    var red: CGFloat = 0
    var green: CGFloat = 0
    var blue: CGFloat = 0
    var alpha: CGFloat = 0

    uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

    return String(
      format: "#%02X%02X%02X",
      Int(red * 255),
      Int(green * 255),
      Int(blue * 255))
  }

  /// 从十六进制字符串创建颜色
  init(hex: String) {
    let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
    var int: UInt64 = 0
    Scanner(string: hex).scanHexInt64(&int)
    let a: UInt64
    let r: UInt64
    let g: UInt64
    let b: UInt64

    switch hex.count {
    case 3:  // RGB (12-bit)
      (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
    case 6:  // RGB (24-bit)
      (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
    case 8:  // ARGB (32-bit)
      (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
    default:
      (a, r, g, b) = (255, 0, 0, 0)
    }

    self.init(
      .sRGB,
      red: Double(r) / 255,
      green: Double(g) / 255,
      blue: Double(b) / 255,
      opacity: Double(a) / 255
    )
  }

  /// 调整颜色亮度
  func adjustBrightness(_ amount: Double) -> Color {
    let uiColor = UIColor(self)
    var hue: CGFloat = 0
    var saturation: CGFloat = 0
    var brightness: CGFloat = 0
    var alpha: CGFloat = 0

    uiColor.getHue(&hue, saturation: &saturation, brightness: &brightness, alpha: &alpha)

    brightness = max(0, min(1, brightness + CGFloat(amount)))

    return Color(UIColor(hue: hue, saturation: saturation, brightness: brightness, alpha: alpha))
  }

  /// 调整颜色透明度
  func withAlpha(_ alpha: Double) -> Color {
    return self.opacity(alpha)
  }
}

// MARK: - Color Schemes

/// 颜色方案定义
struct ColorScheme {
  let name: String
  let colors: [String: Color]
}

extension ColorScheme {

  /// 默认亮色方案
  static let light = ColorScheme(
    name: "Light",
    colors: [
      "background": Color.white,
      "surface": Color.gray.opacity(0.1),
      "text": Color.black,
      "accent": Color.blue,
    ]
  )

  /// 默认暗色方案
  static let dark = ColorScheme(
    name: "Dark",
    colors: [
      "background": Color.black,
      "surface": Color.gray.opacity(0.2),
      "text": Color.white,
      "accent": Color.blue,
    ]
  )

  /// 游戏主题方案
  static let game = ColorScheme(
    name: "Game",
    colors: [
      "background": Color(hex: "#1a1a1a"),
      "surface": Color(hex: "#2a2a2a"),
      "text": Color(hex: "#ffffff"),
      "accent": Color(hex: "#00ff88"),
    ]
  )
}

// MARK: - Color Palette

/// 颜色调色板 - 用于设计系统和主题切换
struct ColorPalette {

  // MARK: - Gray Scale
  static let gray50 = Color(hex: "#fafafa")
  static let gray100 = Color(hex: "#f5f5f5")
  static let gray200 = Color(hex: "#eeeeee")
  static let gray300 = Color(hex: "#e0e0e0")
  static let gray400 = Color(hex: "#bdbdbd")
  static let gray500 = Color(hex: "#9e9e9e")
  static let gray600 = Color(hex: "#757575")
  static let gray700 = Color(hex: "#616161")
  static let gray800 = Color(hex: "#424242")
  static let gray900 = Color(hex: "#212121")

  // MARK: - Blue Scale
  static let blue50 = Color(hex: "#e3f2fd")
  static let blue100 = Color(hex: "#bbdefb")
  static let blue200 = Color(hex: "#90caf9")
  static let blue300 = Color(hex: "#64b5f6")
  static let blue400 = Color(hex: "#42a5f5")
  static let blue500 = Color(hex: "#2196f3")
  static let blue600 = Color(hex: "#1e88e5")
  static let blue700 = Color(hex: "#1976d2")
  static let blue800 = Color(hex: "#1565c0")
  static let blue900 = Color(hex: "#0d47a1")

  // MARK: - Green Scale
  static let green50 = Color(hex: "#e8f5e8")
  static let green100 = Color(hex: "#c8e6c9")
  static let green200 = Color(hex: "#a5d6a7")
  static let green300 = Color(hex: "#81c784")
  static let green400 = Color(hex: "#66bb6a")
  static let green500 = Color(hex: "#4caf50")
  static let green600 = Color(hex: "#43a047")
  static let green700 = Color(hex: "#388e3c")
  static let green800 = Color(hex: "#2e7d32")
  static let green900 = Color(hex: "#1b5e20")

  // MARK: - Red Scale
  static let red50 = Color(hex: "#ffebee")
  static let red100 = Color(hex: "#ffcdd2")
  static let red200 = Color(hex: "#ef9a9a")
  static let red300 = Color(hex: "#e57373")
  static let red400 = Color(hex: "#ef5350")
  static let red500 = Color(hex: "#f44336")
  static let red600 = Color(hex: "#e53935")
  static let red700 = Color(hex: "#d32f2f")
  static let red800 = Color(hex: "#c62828")
  static let red900 = Color(hex: "#b71c1c")

  // MARK: - Orange Scale
  static let orange50 = Color(hex: "#fff3e0")
  static let orange100 = Color(hex: "#ffe0b2")
  static let orange200 = Color(hex: "#ffcc80")
  static let orange300 = Color(hex: "#ffb74d")
  static let orange400 = Color(hex: "#ffa726")
  static let orange500 = Color(hex: "#ff9800")
  static let orange600 = Color(hex: "#fb8c00")
  static let orange700 = Color(hex: "#f57c00")
  static let orange800 = Color(hex: "#ef6c00")
  static let orange900 = Color(hex: "#e65100")
}

// MARK: - Accessibility

extension Color {

  /// 检查颜色对比度是否符合无障碍标准
  func contrastRatio(with color: Color) -> Double {
    let luminance1 = self.luminance()
    let luminance2 = color.luminance()
    let lighter = max(luminance1, luminance2)
    let darker = min(luminance1, luminance2)
    return (lighter + 0.05) / (darker + 0.05)
  }

  /// 计算颜色的相对亮度
  private func luminance() -> Double {
    let uiColor = UIColor(self)
    var red: CGFloat = 0
    var green: CGFloat = 0
    var blue: CGFloat = 0
    var alpha: CGFloat = 0

    uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

    func adjust(_ colorComponent: CGFloat) -> Double {
      let c = Double(colorComponent)
      return c <= 0.03928 ? c / 12.92 : pow((c + 0.055) / 1.055, 2.4)
    }

    return 0.2126 * adjust(red) + 0.7152 * adjust(green) + 0.0722 * adjust(blue)
  }

  /// 检查是否符合 WCAG AA 标准
  func meetsWCAGAA(with background: Color) -> Bool {
    return contrastRatio(with: background) >= 4.5
  }

  /// 检查是否符合 WCAG AAA 标准
  func meetsWCAGAAA(with background: Color) -> Bool {
    return contrastRatio(with: background) >= 7.0
  }
}
