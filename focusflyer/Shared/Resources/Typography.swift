//
//  Typography.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import SwiftUI

/// 字体系统 - 统一管理应用的字体样式
extension Font {

  // MARK: - Brand Fonts

  /// 品牌主字体族
  static let brandFontFamily = "SF Pro Display"

  /// 等宽字体族（用于代码和数据显示）
  static let monospaceFontFamily = "SF Mono"

  // MARK: - Heading Styles

  /// 特大标题 - H1
  static let headingXL = Font.custom(brandFontFamily, size: 32)
    .weight(.bold)

  /// 大标题 - H2
  static let headingLarge = Font.custom(brandFontFamily, size: 28)
    .weight(.bold)

  /// 中标题 - H3
  static let headingMedium = Font.custom(brandFontFamily, size: 24)
    .weight(.semibold)

  /// 小标题 - H4
  static let headingSmall = Font.custom(brandFontFamily, size: 20)
    .weight(.semibold)

  /// 最小标题 - H5
  static let headingXS = Font.custom(brandFontFamily, size: 18)
    .weight(.medium)

  // MARK: - Body Text Styles

  /// 大正文
  static let bodyLarge = Font.custom(brandFontFamily, size: 18)
    .weight(.regular)

  /// 标准正文
  static let bodyMedium = Font.custom(brandFontFamily, size: 16)
    .weight(.regular)

  /// 小正文
  static let bodySmall = Font.custom(brandFontFamily, size: 14)
    .weight(.regular)

  /// 最小正文
  static let bodyXS = Font.custom(brandFontFamily, size: 12)
    .weight(.regular)

  // MARK: - UI Component Styles

  /// 按钮文字
  static let buttonText = Font.custom(brandFontFamily, size: 16)
    .weight(.medium)

  /// 标签文字
  static let labelText = Font.custom(brandFontFamily, size: 14)
    .weight(.medium)

  /// 说明文字
  static let captionText = Font.custom(brandFontFamily, size: 12)
    .weight(.regular)

  /// 注释文字
  static let footnoteText = Font.custom(brandFontFamily, size: 10)
    .weight(.regular)

  // MARK: - Special Styles

  /// 代码字体
  static let codeText = Font.custom(monospaceFontFamily, size: 14)
    .weight(.regular)

  /// 数字显示
  static let numberDisplay = Font.custom(monospaceFontFamily, size: 18)
    .weight(.medium)

  /// 游戏UI字体
  static let gameUI = Font.custom(brandFontFamily, size: 16)
    .weight(.bold)

  /// 调试信息字体
  static let debugText = Font.custom(monospaceFontFamily, size: 12)
    .weight(.regular)
}

// MARK: - Typography Scale

/// 字体比例系统
struct TypographyScale {

  // 基础字体大小
  static let base: CGFloat = 16

  // 比例系数（Major Third - 1.25）
  static let ratio: CGFloat = 1.25

  // 字体大小计算
  static let xs = base / pow(ratio, 2)  // 10.24
  static let sm = base / ratio  // 12.8
  static let md = base  // 16
  static let lg = base * ratio  // 20
  static let xl = base * pow(ratio, 2)  // 25
  static let xxl = base * pow(ratio, 3)  // 31.25
  static let xxxl = base * pow(ratio, 4)  // 39.06

  /// 创建缩放字体
  static func scaledFont(size: CGFloat, weight: Font.Weight = .regular) -> Font {
    return Font.custom(Font.brandFontFamily, size: size).weight(weight)
  }
}

// MARK: - Line Height

/// 行高系统
struct LineHeight {

  /// 紧密行高（1.2倍）
  static let tight: CGFloat = 1.2

  /// 标准行高（1.4倍）
  static let normal: CGFloat = 1.4

  /// 宽松行高（1.6倍）
  static let relaxed: CGFloat = 1.6

  /// 非常宽松（1.8倍）
  static let loose: CGFloat = 1.8

  /// 根据字体大小计算行高
  static func calculate(fontSize: CGFloat, multiplier: CGFloat = normal) -> CGFloat {
    return fontSize * multiplier
  }
}

// MARK: - Text Styles

/// 预定义文本样式
struct TextStyle {
  let font: Font
  let lineSpacing: CGFloat
  let letterSpacing: CGFloat

  init(font: Font, lineSpacing: CGFloat = 0, letterSpacing: CGFloat = 0) {
    self.font = font
    self.lineSpacing = lineSpacing
    self.letterSpacing = letterSpacing
  }
}

extension TextStyle {

  /// 标题样式
  static let title1 = TextStyle(
    font: .headingXL,
    lineSpacing: 4,
    letterSpacing: -0.5
  )

  static let title2 = TextStyle(
    font: .headingLarge,
    lineSpacing: 3,
    letterSpacing: -0.3
  )

  static let title3 = TextStyle(
    font: .headingMedium,
    lineSpacing: 2,
    letterSpacing: -0.2
  )

  /// 正文样式
  static let body = TextStyle(
    font: .bodyMedium,
    lineSpacing: 4,
    letterSpacing: 0
  )

  static let bodySecondary = TextStyle(
    font: .bodySmall,
    lineSpacing: 3,
    letterSpacing: 0.2
  )

  /// UI组件样式
  static let button = TextStyle(
    font: .buttonText,
    lineSpacing: 0,
    letterSpacing: 0.5
  )

  static let caption = TextStyle(
    font: .captionText,
    lineSpacing: 2,
    letterSpacing: 0.3
  )
}

// MARK: - View Extensions

extension Text {

  /// 应用文本样式
  func style(_ textStyle: TextStyle) -> some View {
    self
      .font(textStyle.font)
      .lineSpacing(textStyle.lineSpacing)
      .kerning(textStyle.letterSpacing)
  }

  /// 应用标题样式
  func titleStyle(_ level: Int = 1) -> some View {
    switch level {
    case 1:
      return self.style(.title1)
    case 2:
      return self.style(.title2)
    case 3:
      return self.style(.title3)
    default:
      return self.style(.title1)
    }
  }

  /// 应用正文样式
  func bodyStyle(secondary: Bool = false) -> some View {
    if secondary {
      return self.style(.bodySecondary)
    } else {
      return self.style(.body)
    }
  }

  /// 应用按钮样式
  func buttonStyle() -> some View {
    self.style(.button)
  }

  /// 应用说明文字样式
  func captionStyle() -> some View {
    self.style(.caption)
  }
}

// MARK: - Dynamic Type Support

extension Font {

  /// 支持动态字体的标题
  static func dynamicHeading(_ size: CGFloat) -> Font {
    return Font.custom(brandFontFamily, size: size, relativeTo: .title)
  }

  /// 支持动态字体的正文
  static func dynamicBody(_ size: CGFloat) -> Font {
    return Font.custom(brandFontFamily, size: size, relativeTo: .body)
  }

  /// 支持动态字体的说明文字
  static func dynamicCaption(_ size: CGFloat) -> Font {
    return Font.custom(brandFontFamily, size: size, relativeTo: .caption)
  }
}

// MARK: - Font Weight Extensions

extension Font.Weight {

  /// 品牌字重映射
  static let brandThin = Font.Weight.ultraLight
  static let brandLight = Font.Weight.light
  static let brandRegular = Font.Weight.regular
  static let brandMedium = Font.Weight.medium
  static let brandSemibold = Font.Weight.semibold
  static let brandBold = Font.Weight.bold
  static let brandHeavy = Font.Weight.heavy
}

// MARK: - Font Loading

/// 字体加载器
struct FontLoader {

  /// 注册自定义字体
  static func registerCustomFonts() {
    // 注册自定义字体文件
    registerFont(name: "CustomFont-Regular", extension: "ttf")
    registerFont(name: "CustomFont-Bold", extension: "ttf")
    registerFont(name: "CustomFont-Light", extension: "ttf")
  }

  /// 注册单个字体文件
  private static func registerFont(name: String, extension: String) {
    guard let fontURL = Bundle.main.url(forResource: name, withExtension: `extension`),
      let fontData = NSData(contentsOf: fontURL),
      let provider = CGDataProvider(data: fontData),
      let font = CGFont(provider)
    else {
      print("⚠️ 字体加载失败: \(name).\(`extension`)")
      return
    }

    var error: Unmanaged<CFError>?
    if !CTFontManagerRegisterGraphicsFont(font, &error) {
      print("⚠️ 字体注册失败: \(name) - \(error.debugDescription)")
    } else {
      print("✅ 字体注册成功: \(name)")
    }
  }

  /// 检查字体是否可用
  static func isFontAvailable(name: String) -> Bool {
    return UIFont(name: name, size: 12) != nil
  }

  /// 获取所有可用字体
  static func availableFonts() -> [String] {
    return UIFont.familyNames.sorted()
  }
}

// MARK: - Accessibility

extension TextStyle {

  /// 应用无障碍字体大小调整
  func withAccessibilitySize(sizeCategory: ContentSizeCategory) -> TextStyle {
    let scaleFactor = accessibilityScaleFactor(for: sizeCategory)

    // 创建新的字体，应用缩放因子
    let scaledFont = font.weight(.regular)  // 简化处理

    return TextStyle(
      font: scaledFont,
      lineSpacing: lineSpacing * scaleFactor,
      letterSpacing: letterSpacing
    )
  }

  /// 计算无障碍缩放因子
  private func accessibilityScaleFactor(for sizeCategory: ContentSizeCategory) -> CGFloat {
    switch sizeCategory {
    case .extraSmall:
      return 0.8
    case .small:
      return 0.9
    case .medium:
      return 1.0
    case .large:
      return 1.1
    case .extraLarge:
      return 1.2
    case .extraExtraLarge:
      return 1.3
    case .extraExtraExtraLarge:
      return 1.4
    case .accessibilityMedium:
      return 1.6
    case .accessibilityLarge:
      return 1.8
    case .accessibilityExtraLarge:
      return 2.0
    case .accessibilityExtraExtraLarge:
      return 2.2
    case .accessibilityExtraExtraExtraLarge:
      return 2.4
    @unknown default:
      return 1.0
    }
  }
}
