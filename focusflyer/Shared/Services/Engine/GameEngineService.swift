//
//  GameEngineService.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Combine
import Foundation
import SwiftGodotKit

/// 游戏引擎服务实现 - 管理 Godot 引擎的生命周期和状态
@MainActor
final class GameEngineService: @preconcurrency GameEngineServiceProtocol, ObservableObject {

  // MARK: - Published Properties
  @Published private(set) var godotApp: GodotApp?
  @Published private(set) var engineState: GameEngineState = .initializing
  @Published private(set) var currentScene: String = "main"

  // MARK: - Private Properties
  private let loggingService: LoggingServiceProtocol
  private let configurationService: ConfigurationServiceProtocol
  private var cancellables = Set<AnyCancellable>()
  private var hasStartedInitialization = false

  // MARK: - Constants
  private let packFileName = "main.pck"
  private let defaultScene = "main"

  // MARK: - Initialization

  init(loggingService: LoggingServiceProtocol, configurationService: ConfigurationServiceProtocol) {
    self.loggingService = loggingService
    self.configurationService = configurationService

    self.loggingService.logInfo("🎮 GameEngineService 初始化完成")

    // 自动初始化引擎
    autoInitializeEngine()
  }

  // MARK: - Public Methods

  func initializeEngine() async {
    guard !hasStartedInitialization else { return }
    hasStartedInitialization = true

    loggingService.logInfo("开始初始化Godot引擎 - 使用官方推荐方式")
    engineState = .initializing

    // 检查PCK文件
    guard checkPCKFile() else {
      let errorMsg = "PCK文件未找到或损坏"
      loggingService.logError(errorMsg)
      engineState = .error(errorMsg)
      return
    }

    // 初始化引擎
    await performEngineInitialization()
  }

  func switchScene(to sceneName: String) async {
    loggingService.logUserInteraction("切换场景到: \(sceneName)")

    // 更新当前场景记录
    currentScene = sceneName

    // 注意：实际的场景切换需要通过Godot的信号系统或全局变量来实现
    // 这里只是记录场景状态，具体实现取决于Godot项目的设计
    loggingService.logInfo("场景切换请求完成: \(sceneName)")
  }

  func reinitializeEngine() async {
    loggingService.logWarning("重新初始化引擎")

    // 重置状态
    godotApp = nil
    engineState = .initializing
    hasStartedInitialization = false

    // 重新初始化
    await initializeEngine()
  }

  func getEngineStatus() -> String {
    switch engineState {
    case .initializing:
      return "引擎正在初始化..."
    case .ready:
      return "引擎已就绪，当前场景: \(currentScene)"
    case .error(let message):
      return "引擎错误: \(message)"
    }
  }

  // MARK: - Private Methods

  private func autoInitializeEngine() {
    Task {
      await initializeEngine()
    }
  }

  private func checkPCKFile() -> Bool {
    // 尝试多种方式查找PCK文件
    var pckPath: String?

    // 方式1: 从GodotAssets目录中查找
    pckPath = Bundle.main.path(forResource: "main", ofType: "pck", inDirectory: "GodotAssets")

    // 方式2: 直接从bundle根目录查找
    if pckPath == nil {
      pckPath = Bundle.main.path(forResource: "main", ofType: "pck")
    }

    // 方式3: 从Resources目录查找
    if pckPath == nil {
      pckPath = Bundle.main.path(
        forResource: "main", ofType: "pck", inDirectory: "Resources/GodotAssets")
    }

    // 方式4: 列出所有pck文件进行调试
    if pckPath == nil {
      loggingService.logError("PCK文件未找到 - 开始调试搜索")
      if let bundlePath = Bundle.main.resourcePath {
        loggingService.logInfo("Bundle资源路径: \(bundlePath)")
        let resourcesURL = URL(fileURLWithPath: bundlePath)
        do {
          let contents = try FileManager.default.contentsOfDirectory(
            at: resourcesURL, includingPropertiesForKeys: nil, options: [])
          loggingService.logInfo("Bundle根目录内容: \(contents.map { $0.lastPathComponent })")

          // 递归查找所有pck文件
          let pckFiles = contents.filter { $0.pathExtension == "pck" }
          loggingService.logInfo("找到的PCK文件: \(pckFiles)")

          if let firstPCK = pckFiles.first {
            pckPath = firstPCK.path
            loggingService.logInfo("使用找到的PCK文件: \(firstPCK.path)")
          }
        } catch {
          loggingService.logError("无法读取Bundle内容: \(error)")
        }
      }
    }

    guard let finalPath = pckPath else {
      loggingService.logError("PCK文件未找到 - 所有搜索方式都失败")
      return false
    }

    do {
      let attributes = try FileManager.default.attributesOfItem(atPath: finalPath)
      let fileSize = attributes[.size] as? Int64 ?? 0

      loggingService.logInfo("PCK文件检查通过 - 文件大小: \(fileSize) bytes, 路径: \(finalPath)")
      return fileSize > 0
    } catch {
      loggingService.logError("PCK文件检查失败: \(error)")
      return false
    }
  }

  private func performEngineInitialization() async {
    loggingService.logInfo("创建GodotApp实例 - 官方标准方式")

    // 获取正确的PCK文件路径
    guard let pckPath = getPackFilePath() else {
      let errorMsg = "无法获取PCK文件路径"
      loggingService.logError(errorMsg)
      engineState = .error(errorMsg)
      return
    }

    // 按照官方文档的方式创建 GodotApp，使用文件名而不是完整路径
    let app = GodotApp(packFile: "main.pck")

    // 设置引擎状态
    self.godotApp = app
    self.engineState = .ready
    self.currentScene = defaultScene

    loggingService.logInfo("Godot引擎初始化成功 - SwiftGodotKit接管生命周期管理")
  }

  // MARK: - Status and Diagnostics

  func getResourceStatusReport() -> String {
    var report = "=== Godot引擎资源状态 ===\n\n"

    // PCK文件状态
    if let pckPath = getPackFilePath() {
      do {
        let attributes = try FileManager.default.attributesOfItem(atPath: pckPath)
        let fileSize = attributes[.size] as? Int64 ?? 0
        report += "📦 PCK文件: 正常 (\(fileSize) bytes)\n"
        report += "📍 路径: \(pckPath)\n"
      } catch {
        report += "📦 PCK文件: 错误 - \(error)\n"
      }
    } else {
      report += "📦 PCK文件: 未找到\n"
    }

    // 引擎状态
    report += "\n🎮 引擎状态: \(getEngineStatus())\n"
    report += "🎬 当前场景: \(currentScene)\n"

    return report
  }

  func getFullSystemReport() -> String {
    var report = "=== FocusFlyer 游戏引擎系统报告 ===\n\n"

    // 应用信息
    report += "📱 应用信息:\n"
    report += "  版本: \(configurationService.getAppVersion())\n"
    report += "  构建: \(configurationService.getBuildNumber())\n"
    report += "  调试模式: \(configurationService.isDebugBuild ? "开启" : "关闭")\n\n"

    // 资源状态
    report += getResourceStatusReport()

    // 配置信息
    report += "\n⚙️ 引擎配置:\n"
    report += "  详细日志: \(configurationService.enableVerboseLogging ? "启用" : "禁用")\n"
    report += "  性能监控: \(configurationService.enablePerformanceMonitoring ? "启用" : "禁用")\n"

    // 时间戳
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    formatter.timeStyle = .medium
    report += "\n⏰ 生成时间: \(formatter.string(from: Date()))"

    return report
  }

  /// 检查是否有已知的非关键错误
  func hasKnownIssues() -> Bool {
    // 在 iOS 嵌入环境中，某些Godot错误是正常的
    return configurationService.isDebugBuild
  }

  /// 获取已知问题的说明
  func getKnownIssuesDescription() -> String {
    if hasKnownIssues() {
      return """
        已知的非关键问题（开发模式）:
        • Godot窗口管理错误: iOS嵌入环境的正常现象
        • Hang检测警告: 调试模式下的正常现象
        • 这些问题不影响应用功能
        """
    } else {
      return "当前版本运行稳定，无已知问题。"
    }
  }
}

// MARK: - Engine Diagnostics

extension GameEngineService {

  struct EngineDiagnostics {
    let isInitialized: Bool
    let currentState: GameEngineState
    let currentScene: String
    let packFileExists: Bool
    let packFilePath: String?
    let memoryUsage: UInt64
    let initializationTime: TimeInterval?
  }

  func getDiagnostics() -> EngineDiagnostics {
    return EngineDiagnostics(
      isInitialized: godotApp != nil,
      currentState: engineState,
      currentScene: currentScene,
      packFileExists: getPackFilePath() != nil,
      packFilePath: getPackFilePath(),
      memoryUsage: getMemoryUsage(),
      initializationTime: nil  // 可以记录初始化时间
    )
  }

  private func getMemoryUsage() -> UInt64 {
    var info = mach_task_basic_info()
    var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4

    let result = withUnsafeMutablePointer(to: &info) {
      $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
        task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
      }
    }

    return result == KERN_SUCCESS ? info.resident_size : 0
  }

  private func getPackFilePath() -> String? {
    // 尝试多种方式查找PCK文件
    var pckPath: String?

    // 方式1: 从GodotAssets目录中查找
    pckPath = Bundle.main.path(forResource: "main", ofType: "pck", inDirectory: "GodotAssets")

    // 方式2: 直接从bundle根目录查找
    if pckPath == nil {
      pckPath = Bundle.main.path(forResource: "main", ofType: "pck")
    }

    // 方式3: 从Resources目录查找
    if pckPath == nil {
      pckPath = Bundle.main.path(
        forResource: "main", ofType: "pck", inDirectory: "Resources/GodotAssets")
    }

    // 方式4: 递归查找所有pck文件
    if pckPath == nil {
      if let bundlePath = Bundle.main.resourcePath {
        let resourcesURL = URL(fileURLWithPath: bundlePath)
        do {
          let contents = try FileManager.default.contentsOfDirectory(
            at: resourcesURL, includingPropertiesForKeys: nil, options: [])
          let pckFiles = contents.filter { $0.pathExtension == "pck" }
          if let firstPCK = pckFiles.first {
            pckPath = firstPCK.path
          }
        } catch {
          // 忽略错误，继续尝试其他方式
        }
      }
    }

    return pckPath
  }

  private func updateEngineState(_ newState: GameEngineState) async {
    await MainActor.run {
      self.engineState = newState
    }
  }
}

// MARK: - Scene Management

extension GameEngineService {

  func getAvailableScenes() -> [String] {
    // 这里可以返回可用的场景列表
    // 实际实现需要从 Godot 项目中读取场景信息
    return [
      "main",
      "game",
      "menu",
      "settings",
    ]
  }

  func isSceneValid(_ sceneName: String) -> Bool {
    return getAvailableScenes().contains(sceneName)
  }

  func getSceneDisplayName(_ sceneName: String) -> String {
    switch sceneName {
    case "main":
      return "主场景"
    case "game":
      return "游戏场景"
    case "menu":
      return "菜单"
    case "settings":
      return "设置"
    default:
      return sceneName.capitalized
    }
  }
}

// MARK: - Performance Monitoring

extension GameEngineService {

  struct PerformanceMetrics {
    let frameRate: Double
    let memoryUsage: UInt64
    let cpuUsage: Double
    let renderTime: TimeInterval
    let updateTime: TimeInterval
  }

  func getPerformanceMetrics() -> PerformanceMetrics {
    // 简化实现，实际应该从 Godot 引擎获取性能数据
    return PerformanceMetrics(
      frameRate: 60.0,
      memoryUsage: getMemoryUsage(),
      cpuUsage: getCurrentCPUUsage(),
      renderTime: 0.016,  // 16ms for 60fps
      updateTime: 0.008  // 8ms for update
    )
  }

  private func getCurrentCPUUsage() -> Double {
    var info = mach_task_basic_info()
    var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4

    let result = withUnsafeMutablePointer(to: &info) {
      $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
        task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
      }
    }

    // 简化的 CPU 使用率计算
    return result == KERN_SUCCESS ? Double(info.resident_size) / 1000000.0 : 0.0
  }
}

// MARK: - Error Handling

extension GameEngineService {

  enum EngineError: LocalizedError {
    case packFileNotFound(String)
    case initializationFailed(String)
    case sceneNotFound(String)
    case engineNotReady

    var errorDescription: String? {
      switch self {
      case .packFileNotFound(let fileName):
        return "资源包文件未找到: \(fileName)"
      case .initializationFailed(let reason):
        return "引擎初始化失败: \(reason)"
      case .sceneNotFound(let sceneName):
        return "场景未找到: \(sceneName)"
      case .engineNotReady:
        return "引擎尚未就绪"
      }
    }
  }

  func handleError(_ error: EngineError) {
    loggingService.logError("引擎错误: \(error.localizedDescription)")

    Task {
      await updateEngineState(.error(error.localizedDescription))
    }
  }
}
