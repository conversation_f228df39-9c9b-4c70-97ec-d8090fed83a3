//
//  StorageService.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Foundation
import Security

/// 存储服务实现 - 提供 UserDefaults、Keychain 和文件存储功能
final class StorageService: StorageServiceProtocol {

  // MARK: - Properties
  private let userDefaults = UserDefaults.standard
  private let fileManager = FileManager.default
  private let encoder = JSONEncoder()
  private let decoder = JSONDecoder()

  // MARK: - Document Directory
  private var documentsDirectory: URL {
    fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
  }

  init() {
    encoder.dateEncodingStrategy = .iso8601
    decoder.dateDecodingStrategy = .iso8601
  }

  // MARK: - UserDefaults Operations

  func setValue<T>(_ value: T, forKey key: String) where T: Codable {
    do {
      let data = try encoder.encode(value)
      userDefaults.set(data, forKey: key)
    } catch {
      print("❌ Failed to encode value for key \(key): \(error)")
    }
  }

  func getValue<T>(forKey key: String, type: T.Type) -> T? where T: Codable {
    guard let data = userDefaults.data(forKey: key) else { return nil }

    do {
      return try decoder.decode(type, from: data)
    } catch {
      print("❌ Failed to decode value for key \(key): \(error)")
      return nil
    }
  }

  func removeValue(forKey key: String) {
    userDefaults.removeObject(forKey: key)
  }

  // MARK: - Keychain Operations

  func setSecureValue(_ value: String, forKey key: String) -> Bool {
    let data = Data(value.utf8)

    // Delete existing item first
    let deleteQuery: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrAccount as String: key,
    ]
    SecItemDelete(deleteQuery as CFDictionary)

    // Add new item
    let addQuery: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrAccount as String: key,
      kSecValueData as String: data,
      kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly,
    ]

    let status = SecItemAdd(addQuery as CFDictionary, nil)
    return status == errSecSuccess
  }

  func getSecureValue(forKey key: String) -> String? {
    let query: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrAccount as String: key,
      kSecReturnData as String: true,
      kSecMatchLimit as String: kSecMatchLimitOne,
    ]

    var dataTypeRef: AnyObject?
    let status = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)

    guard status == errSecSuccess,
      let data = dataTypeRef as? Data
    else {
      return nil
    }

    return String(data: data, encoding: .utf8)
  }

  func removeSecureValue(forKey key: String) -> Bool {
    let query: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrAccount as String: key,
    ]

    let status = SecItemDelete(query as CFDictionary)
    return status == errSecSuccess || status == errSecItemNotFound
  }

  // MARK: - File Operations

  func saveToDocuments<T>(_ object: T, fileName: String) throws where T: Codable {
    let url = documentsDirectory.appendingPathComponent(fileName)
    let data = try encoder.encode(object)
    try data.write(to: url)
  }

  func loadFromDocuments<T>(_ type: T.Type, fileName: String) throws -> T where T: Codable {
    let url = documentsDirectory.appendingPathComponent(fileName)
    let data = try Data(contentsOf: url)
    return try decoder.decode(type, from: data)
  }

  func deleteFromDocuments(fileName: String) throws {
    let url = documentsDirectory.appendingPathComponent(fileName)
    try fileManager.removeItem(at: url)
  }

  // MARK: - Helper Methods

  /// 检查文件是否存在
  func fileExists(fileName: String) -> Bool {
    let url = documentsDirectory.appendingPathComponent(fileName)
    return fileManager.fileExists(atPath: url.path)
  }

  /// 获取文件大小
  func getFileSize(fileName: String) -> Int64? {
    let url = documentsDirectory.appendingPathComponent(fileName)
    do {
      let attributes = try fileManager.attributesOfItem(atPath: url.path)
      return attributes[.size] as? Int64
    } catch {
      return nil
    }
  }

  /// 清理过期文件
  func cleanupOldFiles(olderThan days: Int) throws {
    let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()

    let contents = try fileManager.contentsOfDirectory(
      at: documentsDirectory,
      includingPropertiesForKeys: [.creationDateKey],
      options: .skipsHiddenFiles)

    for url in contents {
      let attributes = try url.resourceValues(forKeys: [.creationDateKey])
      if let creationDate = attributes.creationDate, creationDate < cutoffDate {
        try fileManager.removeItem(at: url)
      }
    }
  }

  /// 获取存储使用情况
  func getStorageUsage() -> StorageUsage {
    let documentsSize = getDirectorySize(documentsDirectory)
    let userDefaultsSize = getUserDefaultsSize()

    return StorageUsage(
      documentsSize: documentsSize,
      userDefaultsSize: userDefaultsSize,
      totalSize: documentsSize + userDefaultsSize
    )
  }

  private func getDirectorySize(_ directory: URL) -> UInt64 {
    var size: UInt64 = 0

    if let enumerator = fileManager.enumerator(
      at: directory,
      includingPropertiesForKeys: [.fileSizeKey],
      options: .skipsHiddenFiles)
    {
      for case let url as URL in enumerator {
        do {
          let attributes = try url.resourceValues(forKeys: [.fileSizeKey])
          size += UInt64(attributes.fileSize ?? 0)
        } catch {
          continue
        }
      }
    }

    return size
  }

  private func getUserDefaultsSize() -> UInt64 {
    // 估算 UserDefaults 大小（这是一个近似值）
    let defaults = userDefaults.dictionaryRepresentation()
    do {
      let data = try JSONSerialization.data(withJSONObject: defaults)
      return UInt64(data.count)
    } catch {
      return 0
    }
  }
}

// MARK: - Storage Usage

struct StorageUsage {
  let documentsSize: UInt64
  let userDefaultsSize: UInt64
  let totalSize: UInt64

  var documentsFormattedSize: String {
    ByteCountFormatter.string(fromByteCount: Int64(documentsSize), countStyle: .file)
  }

  var userDefaultsFormattedSize: String {
    ByteCountFormatter.string(fromByteCount: Int64(userDefaultsSize), countStyle: .file)
  }

  var totalFormattedSize: String {
    ByteCountFormatter.string(fromByteCount: Int64(totalSize), countStyle: .file)
  }
}

// MARK: - Storage Keys

enum StorageKeys {
  // Game related
  static let gameState = "game_state"
  static let scenePreferences = "scene_preferences"
  static let gameSettings = "game_settings"

  // User related
  static let userPreferences = "user_preferences"
  static let appSettings = "app_settings"

  // Debug and logging
  static let logEntries = "log_entries"
  static let debugSettings = "debug_settings"

  // Animation
  static let animationSettings = "animation_settings"
  static let lastPlayedAnimation = "last_played_animation"

  // Onboarding
  static let hasSeenOnboarding = "has_seen_onboarding"
  static let firstLaunchDate = "first_launch_date"

  // Performance
  static let performanceMetrics = "performance_metrics"
  static let lastCrashReport = "last_crash_report"
}

// MARK: - Secure Storage Keys

enum SecureStorageKeys {
  static let userToken = "user_token"
  static let deviceID = "device_id"
  static let encryptionKey = "encryption_key"
}
