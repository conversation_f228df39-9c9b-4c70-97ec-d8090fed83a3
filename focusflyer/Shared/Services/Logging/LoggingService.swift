//
//  LoggingService.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Combine
import Foundation
import OSLog

/// 日志服务实现 - 提供结构化日志记录和管理功能
final class LoggingService: LoggingServiceProtocol, ObservableObject {

  // MARK: - Properties
  @Published private(set) var logEntries: [LogEntry] = []

  private let osLogger = Logger(
    subsystem: Bundle.main.bundleIdentifier ?? "focusflyer", category: "app")
  private let maxLogEntries: Int = 1000
  private let logQueue = DispatchQueue(label: "com.focusflyer.logging", qos: .utility)
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Configuration
  private var enableConsoleLogging: Bool = true
  private var enableFileLogging: Bool = true
  private var enableOSLogging: Bool = true
  private var minLogLevel: LogLevel = .debug

  init() {
    setupConfiguration()
    loadLogHistory()
  }

  // MARK: - Configuration Setup

  private func setupConfiguration() {
    #if DEBUG
      enableConsoleLogging = true
      enableFileLogging = true
      minLogLevel = .debug
    #else
      enableConsoleLogging = false
      enableFileLogging = true
      minLogLevel = .info
    #endif
  }

  // MARK: - Public Logging Methods

  func logDebug(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    log(message: message, level: .debug, file: file, function: function, line: line)
  }

  func logInfo(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    log(message: message, level: .info, file: file, function: function, line: line)
  }

  func logWarning(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    log(message: message, level: .warning, file: file, function: function, line: line)
  }

  func logError(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    log(message: message, level: .error, file: file, function: function, line: line)
  }

  func logUserInteraction(
    _ action: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    let message = "👆 User Interaction: \(action)"
    log(
      message: message, level: .info, category: "user_interaction", file: file, function: function,
      line: line)
  }

  func logPerformance(
    operation: String, duration: TimeInterval, file: String = #file, function: String = #function,
    line: Int = #line
  ) {
    let message = "⏱️ Performance: \(operation) completed in \(String(format: "%.3f", duration))s"
    log(
      message: message, level: .info, category: "performance", file: file, function: function,
      line: line)
  }

  // MARK: - Core Logging Method

  private func log(
    message: String, level: LogLevel, category: String? = nil, file: String, function: String,
    line: Int
  ) {
    // 检查日志级别
    guard level.rawValue >= minLogLevel.rawValue else { return }

    let fileName = URL(fileURLWithPath: file).lastPathComponent

    logQueue.async { [weak self] in
      guard let self = self else { return }

      // 创建日志条目
      let entry = LogEntry(
        timestamp: Date(),
        level: level,
        message: message,
        file: fileName,
        function: function,
        line: line,
        category: category
      )

      // 添加到内存日志
      DispatchQueue.main.async {
        self.addLogEntry(entry)
      }

      // 控制台输出
      if self.enableConsoleLogging {
        self.logToConsole(entry)
      }

      // 系统日志
      if self.enableOSLogging {
        self.logToOSLog(entry)
      }

      // 文件日志
      if self.enableFileLogging {
        self.logToFile(entry)
      }
    }
  }

  // MARK: - Log Output Methods

  private func addLogEntry(_ entry: LogEntry) {
    logEntries.append(entry)

    // 保持日志数量限制
    if logEntries.count > maxLogEntries {
      logEntries.removeFirst(logEntries.count - maxLogEntries)
    }

    // 自动保存到持久化存储
    saveLogHistory()
  }

  private func logToConsole(_ entry: LogEntry) {
    let timestamp = formatTimestamp(entry.timestamp)
    let location = "\(entry.file):\(entry.line)"
    print("\(entry.level.emoji) [\(timestamp)] [\(location)] \(entry.message)")
  }

  private func logToOSLog(_ entry: LogEntry) {
    let message = "[\(entry.file):\(entry.line)] \(entry.message)"

    switch entry.level {
    case .debug:
      osLogger.debug("\(message)")
    case .info:
      osLogger.info("\(message)")
    case .warning:
      osLogger.warning("\(message)")
    case .error:
      osLogger.error("\(message)")
    }
  }

  private func logToFile(_ entry: LogEntry) {
    Task {
      do {
        let logString = formatLogEntry(entry)
        try await appendToLogFile(logString)
      } catch {
        print("❌ Failed to write log to file: \(error)")
      }
    }
  }

  // MARK: - Log Management

  func getLogHistory() -> [LogEntry] {
    return logEntries
  }

  func clearLogs() {
    logQueue.async { [weak self] in
      DispatchQueue.main.async {
        self?.logEntries.removeAll()
        self?.saveLogHistory()
      }
    }

    // 清理日志文件
    Task {
      try? await clearLogFile()
    }
  }

  func generateReport() -> String {
    let entries = getLogHistory()
    var report = "📋 FocusFlyer 日志报告\n"
    report += "==================\n\n"
    report += "生成时间: \(formatTimestamp(Date()))\n"
    report += "日志条目数: \(entries.count)\n\n"

    // 统计各级别日志数量
    let levelCounts = Dictionary(grouping: entries, by: { $0.level })
      .mapValues { $0.count }

    report += "日志级别统计:\n"
    for level in LogLevel.allCases {
      let count = levelCounts[level] ?? 0
      report += "  \(level.emoji) \(level): \(count)\n"
    }

    report += "\n详细日志:\n"
    report += "----------\n\n"

    for entry in entries.suffix(100) {  // 只包含最近100条
      report += formatLogEntry(entry) + "\n"
    }

    return report
  }

  // MARK: - Filtering and Search

  func getLogEntries(level: LogLevel) -> [LogEntry] {
    return logEntries.filter { $0.level == level }
  }

  func getLogEntries(category: String) -> [LogEntry] {
    return logEntries.filter { $0.category == category }
  }

  func getLogEntries(containing searchText: String) -> [LogEntry] {
    return logEntries.filter { $0.message.localizedCaseInsensitiveContains(searchText) }
  }

  func getLogEntries(since date: Date) -> [LogEntry] {
    return logEntries.filter { $0.timestamp >= date }
  }

  // MARK: - Configuration

  func updateConfiguration(
    enableConsole: Bool? = nil,
    enableFile: Bool? = nil,
    enableOSLog: Bool? = nil,
    minLevel: LogLevel? = nil
  ) {
    if let enableConsole = enableConsole {
      self.enableConsoleLogging = enableConsole
    }
    if let enableFile = enableFile {
      self.enableFileLogging = enableFile
    }
    if let enableOSLog = enableOSLog {
      self.enableOSLogging = enableOSLog
    }
    if let minLevel = minLevel {
      self.minLogLevel = minLevel
    }

    logInfo("日志配置已更新")
  }

  // MARK: - Helper Methods

  private func formatTimestamp(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateFormat = "HH:mm:ss.SSS"
    return formatter.string(from: date)
  }

  private func formatLogEntry(_ entry: LogEntry) -> String {
    let timestamp = formatTimestamp(entry.timestamp)
    let category = entry.category.map { "[\($0)]" } ?? ""
    return
      "\(entry.level.emoji) [\(timestamp)] \(category) [\(entry.file):\(entry.line)] \(entry.message)"
  }

  // MARK: - File Logging

  private var logFileURL: URL {
    let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
      .first!
    return documentsPath.appendingPathComponent("focusflyer.log")
  }

  private func appendToLogFile(_ logString: String) async throws {
    let logLine = logString + "\n"
    let data = Data(logLine.utf8)

    if FileManager.default.fileExists(atPath: logFileURL.path) {
      let fileHandle = try FileHandle(forWritingTo: logFileURL)
      defer { fileHandle.closeFile() }
      fileHandle.seekToEndOfFile()
      fileHandle.write(data)
    } else {
      try data.write(to: logFileURL)
    }
  }

  private func clearLogFile() async throws {
    if FileManager.default.fileExists(atPath: logFileURL.path) {
      try FileManager.default.removeItem(at: logFileURL)
    }
  }

  // MARK: - Persistence

  private func saveLogHistory() {
    // 只保存最近的日志条目到 UserDefaults
    let recentEntries = Array(logEntries.suffix(50))

    do {
      let encoder = JSONEncoder()
      encoder.dateEncodingStrategy = .iso8601
      let data = try encoder.encode(recentEntries)
      UserDefaults.standard.set(data, forKey: StorageKeys.logEntries)
    } catch {
      print("❌ Failed to save log history: \(error)")
    }
  }

  private func loadLogHistory() {
    guard let data = UserDefaults.standard.data(forKey: StorageKeys.logEntries) else { return }

    do {
      let decoder = JSONDecoder()
      decoder.dateDecodingStrategy = .iso8601
      let entries = try decoder.decode([LogEntry].self, from: data)
      logEntries = entries
    } catch {
      print("❌ Failed to load log history: \(error)")
    }
  }

  // MARK: - Export

  func exportLogs() async throws -> URL {
    let report = generateReport()
    let exportURL = FileManager.default.temporaryDirectory
      .appendingPathComponent("focusflyer_logs_\(Int(Date().timeIntervalSince1970)).txt")

    try report.write(to: exportURL, atomically: true, encoding: .utf8)
    return exportURL
  }
}

// MARK: - Log Statistics

extension LoggingService {

  struct LogStatistics {
    let totalEntries: Int
    let debugCount: Int
    let infoCount: Int
    let warningCount: Int
    let errorCount: Int
    let userInteractionCount: Int
    let performanceCount: Int
    let oldestEntry: Date?
    let newestEntry: Date?
  }

  func getStatistics() -> LogStatistics {
    let entries = getLogHistory()
    let levelCounts = Dictionary(grouping: entries, by: { $0.level }).mapValues { $0.count }
    let categoryCounts = Dictionary(grouping: entries, by: { $0.category }).mapValues { $0.count }

    return LogStatistics(
      totalEntries: entries.count,
      debugCount: levelCounts[.debug] ?? 0,
      infoCount: levelCounts[.info] ?? 0,
      warningCount: levelCounts[.warning] ?? 0,
      errorCount: levelCounts[.error] ?? 0,
      userInteractionCount: categoryCounts["user_interaction"] ?? 0,
      performanceCount: categoryCounts["performance"] ?? 0,
      oldestEntry: entries.first?.timestamp,
      newestEntry: entries.last?.timestamp
    )
  }
}
