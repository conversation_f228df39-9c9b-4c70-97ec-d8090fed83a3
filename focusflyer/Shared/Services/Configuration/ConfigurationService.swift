//
//  ConfigurationService.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Foundation
import UIKit

/// 配置服务实现 - 管理应用配置和构建设置
final class ConfigurationService: ConfigurationServiceProtocol {

  // MARK: - Build Configuration

  var isDebugBuild: Bool {
    #if DEBUG
      return true
    #else
      return false
    #endif
  }

  var isReleaseBuild: Bool {
    return !isDebugBuild
  }

  // MARK: - Debug Features

  var enableDebugPanel: Bool {
    #if DEBUG
      return true
    #else
      return UserDefaults.standard.bool(forKey: "enable_debug_panel")
    #endif
  }

  var enableVerboseLogging: Bool {
    #if DEBUG
      return true
    #else
      return UserDefaults.standard.bool(forKey: "enable_verbose_logging")
    #endif
  }

  var enablePerformanceMonitoring: Bool {
    #if DEBUG
      return true
    #else
      return UserDefaults.standard.bool(forKey: "enable_performance_monitoring")
    #endif
  }

  // MARK: - App Information

  func getAppVersion() -> String {
    return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
  }

  func getBuildNumber() -> String {
    return Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
  }

  func getAppName() -> String {
    return Bundle.main.infoDictionary?["CFBundleDisplayName"] as? String ?? "FocusFlyer"
  }

  func getBundleIdentifier() -> String {
    return Bundle.main.bundleIdentifier ?? "com.focusflyer.app"
  }

  // MARK: - Log Level Configuration

  func shouldLog(level: LogLevel) -> Bool {
    let minLevel = getMinimumLogLevel()
    return level.rawValue >= minLevel.rawValue
  }

  private func getMinimumLogLevel() -> LogLevel {
    #if DEBUG
      return .debug
    #else
      let levelRaw = UserDefaults.standard.integer(forKey: "minimum_log_level")
      return LogLevel(rawValue: levelRaw) ?? .info
    #endif
  }

  // MARK: - Performance Configuration

  struct PerformanceConfig {
    let enableFrameRateMonitoring: Bool
    let enableMemoryMonitoring: Bool
    let enableBatteryMonitoring: Bool
    let enableThermalMonitoring: Bool
    let performanceReportingInterval: TimeInterval
  }

  func getPerformanceConfig() -> PerformanceConfig {
    return PerformanceConfig(
      enableFrameRateMonitoring: enablePerformanceMonitoring,
      enableMemoryMonitoring: enablePerformanceMonitoring,
      enableBatteryMonitoring: enablePerformanceMonitoring,
      enableThermalMonitoring: enablePerformanceMonitoring,
      performanceReportingInterval: isDebugBuild ? 1.0 : 30.0
    )
  }

  // MARK: - Game Configuration

  struct GameConfig {
    let enableAutoSave: Bool
    let autoSaveInterval: TimeInterval
    let maxSaveSlots: Int
    let enableCloudSync: Bool
    let defaultGraphicsQuality: GameSettings.GraphicsQuality
  }

  func getGameConfig() -> GameConfig {
    return GameConfig(
      enableAutoSave: UserDefaults.standard.object(forKey: "enable_auto_save") as? Bool ?? true,
      autoSaveInterval: UserDefaults.standard.object(forKey: "auto_save_interval") as? TimeInterval
        ?? 300.0,
      maxSaveSlots: UserDefaults.standard.object(forKey: "max_save_slots") as? Int ?? 5,
      enableCloudSync: UserDefaults.standard.object(forKey: "enable_cloud_sync") as? Bool ?? false,
      defaultGraphicsQuality: getDefaultGraphicsQuality()
    )
  }

  private func getDefaultGraphicsQuality() -> GameSettings.GraphicsQuality {
    let qualityString = UserDefaults.standard.string(forKey: "default_graphics_quality") ?? "medium"
    return GameSettings.GraphicsQuality(rawValue: qualityString) ?? .medium
  }

  // MARK: - Animation Configuration

  struct AnimationConfig {
    let enableAnimations: Bool
    let animationDuration: TimeInterval
    let enableParticles: Bool
    let maxParticleCount: Int
  }

  func getAnimationConfig() -> AnimationConfig {
    return AnimationConfig(
      enableAnimations: UserDefaults.standard.object(forKey: "enable_animations") as? Bool ?? true,
      animationDuration: UserDefaults.standard.object(forKey: "animation_duration") as? TimeInterval
        ?? 0.3,
      enableParticles: UserDefaults.standard.object(forKey: "enable_particles") as? Bool ?? true,
      maxParticleCount: UserDefaults.standard.object(forKey: "max_particle_count") as? Int ?? 100
    )
  }

  // MARK: - Storage Configuration

  struct StorageConfig {
    let enableEncryption: Bool
    let maxCacheSize: UInt64
    let enableBackup: Bool
    let backupFrequency: AppSettings.BackupFrequency
  }

  func getStorageConfig() -> StorageConfig {
    return StorageConfig(
      enableEncryption: UserDefaults.standard.object(forKey: "enable_encryption") as? Bool ?? true,
      maxCacheSize: UserDefaults.standard.object(forKey: "max_cache_size") as? UInt64
        ?? 100_000_000,  // 100MB
      enableBackup: UserDefaults.standard.object(forKey: "enable_backup") as? Bool ?? true,
      backupFrequency: getBackupFrequency()
    )
  }

  private func getBackupFrequency() -> AppSettings.BackupFrequency {
    let frequencyString = UserDefaults.standard.string(forKey: "backup_frequency") ?? "daily"
    return AppSettings.BackupFrequency(rawValue: frequencyString) ?? .daily
  }

  // MARK: - Feature Flags

  struct FeatureFlags {
    let enableNewGameEngine: Bool
    let enableAdvancedGraphics: Bool
    let enableCloudSave: Bool
    let enablePushNotifications: Bool
    let enableAnalytics: Bool
    let enableCrashReporting: Bool
  }

  func getFeatureFlags() -> FeatureFlags {
    return FeatureFlags(
      enableNewGameEngine: getFeatureFlag("new_game_engine", defaultValue: false),
      enableAdvancedGraphics: getFeatureFlag("advanced_graphics", defaultValue: true),
      enableCloudSave: getFeatureFlag("cloud_save", defaultValue: false),
      enablePushNotifications: getFeatureFlag("push_notifications", defaultValue: true),
      enableAnalytics: getFeatureFlag("analytics", defaultValue: false),
      enableCrashReporting: getFeatureFlag("crash_reporting", defaultValue: true)
    )
  }

  private func getFeatureFlag(_ key: String, defaultValue: Bool) -> Bool {
    // 在 DEBUG 模式下，可以通过 UserDefaults 覆盖
    #if DEBUG
      let userDefaultsKey = "feature_flag_\(key)"
      if UserDefaults.standard.object(forKey: userDefaultsKey) != nil {
        return UserDefaults.standard.bool(forKey: userDefaultsKey)
      }
    #endif

    return defaultValue
  }

  // MARK: - Device Capabilities

  struct DeviceCapabilities {
    let supportsHapticFeedback: Bool
    let supportsBiometrics: Bool
    let supportsDepthCamera: Bool
    let supportsARKit: Bool
    let memoryCapacity: UInt64
    let storageCapacity: UInt64
  }

  func getDeviceCapabilities() -> DeviceCapabilities {
    return DeviceCapabilities(
      supportsHapticFeedback: hasHapticFeedbackSupport(),
      supportsBiometrics: hasBiometricSupport(),
      supportsDepthCamera: hasDepthCameraSupport(),
      supportsARKit: hasARKitSupport(),
      memoryCapacity: getMemoryCapacity(),
      storageCapacity: getStorageCapacity()
    )
  }

  private func hasHapticFeedbackSupport() -> Bool {
    return UIDevice.current.userInterfaceIdiom == .phone
  }

  private func hasBiometricSupport() -> Bool {
    // 简化实现，实际应该检查 LocalAuthentication
    return true
  }

  private func hasDepthCameraSupport() -> Bool {
    // 简化实现，实际应该检查设备型号
    return false
  }

  private func hasARKitSupport() -> Bool {
    // 简化实现，实际应该检查 ARConfiguration 支持
    return true
  }

  private func getMemoryCapacity() -> UInt64 {
    return ProcessInfo.processInfo.physicalMemory
  }

  private func getStorageCapacity() -> UInt64 {
    // 简化实现，实际应该检查设备存储容量
    return 64_000_000_000  // 64GB 默认值
  }

  // MARK: - Runtime Configuration Updates

  func updateConfiguration(_ key: String, value: Any) {
    UserDefaults.standard.set(value, forKey: key)
    NotificationCenter.default.post(
      name: .configurationDidChange,
      object: nil,
      userInfo: [key: value]
    )
  }

  func resetToDefaults() {
    let defaultKeys = [
      "enable_debug_panel",
      "enable_verbose_logging",
      "enable_performance_monitoring",
      "minimum_log_level",
      "enable_auto_save",
      "auto_save_interval",
      "max_save_slots",
      "enable_cloud_sync",
      "default_graphics_quality",
      "enable_animations",
      "animation_duration",
      "enable_particles",
      "max_particle_count",
      "enable_encryption",
      "max_cache_size",
      "enable_backup",
      "backup_frequency",
    ]

    for key in defaultKeys {
      UserDefaults.standard.removeObject(forKey: key)
    }

    NotificationCenter.default.post(name: .configurationDidReset, object: nil)
  }

  // MARK: - Configuration Export/Import

  func exportConfiguration() -> [String: Any] {
    var config: [String: Any] = [:]

    config["app_version"] = getAppVersion()
    config["build_number"] = getBuildNumber()
    config["is_debug_build"] = isDebugBuild
    config["enable_debug_panel"] = enableDebugPanel
    config["enable_verbose_logging"] = enableVerboseLogging
    config["enable_performance_monitoring"] = enablePerformanceMonitoring

    // 添加用户配置
    let userDefaults = UserDefaults.standard.dictionaryRepresentation()
    for (key, value) in userDefaults {
      if key.hasPrefix("focusflyer_") || key.contains("_") {
        config[key] = value
      }
    }

    return config
  }

  func importConfiguration(_ config: [String: Any]) {
    for (key, value) in config {
      if !key.hasPrefix("app_") && !key.hasPrefix("build_") && !key.hasPrefix("is_debug") {
        UserDefaults.standard.set(value, forKey: key)
      }
    }

    NotificationCenter.default.post(name: .configurationDidImport, object: nil)
  }
}

// MARK: - Notification Names

extension Notification.Name {
  static let configurationDidChange = Notification.Name("configurationDidChange")
  static let configurationDidReset = Notification.Name("configurationDidReset")
  static let configurationDidImport = Notification.Name("configurationDidImport")
}

// MARK: - Configuration Keys

enum ConfigurationKeys {
  // Debug
  static let enableDebugPanel = "enable_debug_panel"
  static let enableVerboseLogging = "enable_verbose_logging"
  static let enablePerformanceMonitoring = "enable_performance_monitoring"
  static let minimumLogLevel = "minimum_log_level"

  // Game
  static let enableAutoSave = "enable_auto_save"
  static let autoSaveInterval = "auto_save_interval"
  static let maxSaveSlots = "max_save_slots"
  static let enableCloudSync = "enable_cloud_sync"
  static let defaultGraphicsQuality = "default_graphics_quality"

  // Animation
  static let enableAnimations = "enable_animations"
  static let animationDuration = "animation_duration"
  static let enableParticles = "enable_particles"
  static let maxParticleCount = "max_particle_count"

  // Storage
  static let enableEncryption = "enable_encryption"
  static let maxCacheSize = "max_cache_size"
  static let enableBackup = "enable_backup"
  static let backupFrequency = "backup_frequency"

  // Feature Flags
  static let featureFlagPrefix = "feature_flag_"
}
