//
//  ServiceProtocols.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Combine
import Foundation
import SwiftGodotKit

// MARK: - Game Engine Service Protocol

protocol GameEngineServiceProtocol: ObservableObject {
  var godotApp: GodotApp? { get }
  var engineState: GameEngineState { get }
  var currentScene: String { get }

  func initializeEngine() async
  func switchScene(to sceneName: String) async
  func reinitializeEngine() async
  func getEngineStatus() -> String
}

// MARK: - Storage Service Protocol

protocol StorageServiceProtocol {
  // UserDefaults 操作
  func setValue<T>(_ value: T, forKey key: String) where T: Codable
  func getValue<T>(forKey key: String, type: T.Type) -> T? where T: Codable
  func removeValue(forKey key: String)

  // Keychain 操作
  func setSecureValue(_ value: String, forKey key: String) -> Bool
  func getSecureValue(forKey key: String) -> String?
  func removeSecureValue(forKey key: String) -> Bool

  // 文件操作
  func saveToDocuments<T>(_ object: T, fileName: String) throws where T: Codable
  func loadFromDocuments<T>(_ type: T.Type, fileName: String) throws -> T where T: Codable
  func deleteFromDocuments(fileName: String) throws
  func fileExists(fileName: String) -> Bool
  func getFileSize(fileName: String) -> Int64?
}

// MARK: - Logging Service Protocol

protocol LoggingServiceProtocol {
  func logDebug(_ message: String, file: String, function: String, line: Int)
  func logInfo(_ message: String, file: String, function: String, line: Int)
  func logWarning(_ message: String, file: String, function: String, line: Int)
  func logError(_ message: String, file: String, function: String, line: Int)

  func logUserInteraction(_ action: String, file: String, function: String, line: Int)
  func logPerformance(
    operation: String, duration: TimeInterval, file: String, function: String, line: Int)

  func getLogHistory() -> [LogEntry]
  func clearLogs()
  func generateReport() -> String
}

// MARK: - Configuration Service Protocol

protocol ConfigurationServiceProtocol {
  var isDebugBuild: Bool { get }
  var isReleaseBuild: Bool { get }
  var enableDebugPanel: Bool { get }
  var enableVerboseLogging: Bool { get }
  var enablePerformanceMonitoring: Bool { get }

  func shouldLog(level: LogLevel) -> Bool
  func getAppVersion() -> String
  func getBuildNumber() -> String
}

// MARK: - Animation Service Protocol

protocol AnimationServiceProtocol: ObservableObject {
  var isAnimationPlaying: Bool { get }
  var currentAnimation: String? { get }

  func playAnimation(named name: String) async
  func stopAnimation() async
  func pauseAnimation() async
  func resumeAnimation() async
}

// MARK: - Repository Protocols

protocol GameRepositoryProtocol {
  func saveGameState(_ state: GameState) async throws
  func loadGameState() async throws -> GameState?
  func deleteGameState() async throws

  func saveScenePreferences(_ preferences: ScenePreferences) async throws
  func loadScenePreferences() async throws -> ScenePreferences
}

protocol SettingsRepositoryProtocol {
  func saveUserPreferences(_ preferences: UserPreferences) async throws
  func loadUserPreferences() async throws -> UserPreferences

  func saveAppSettings(_ settings: AppSettings) async throws
  func loadAppSettings() async throws -> AppSettings
}

// MARK: - Supporting Types

enum GameEngineState: Equatable {
  case initializing
  case ready
  case error(String)

  static func == (lhs: GameEngineState, rhs: GameEngineState) -> Bool {
    switch (lhs, rhs) {
    case (.initializing, .initializing), (.ready, .ready):
      return true
    case (.error(let lhsMessage), (.error(let rhsMessage))):
      return lhsMessage == rhsMessage
    default:
      return false
    }
  }
}

public enum LogLevel: Int, CaseIterable, Codable {
  case debug = 0
  case info = 1
  case warning = 2
  case error = 3

  var emoji: String {
    switch self {
    case .debug: return "🔍"
    case .info: return "ℹ️"
    case .warning: return "⚠️"
    case .error: return "❌"
    }
  }
}

struct LogEntry: Identifiable, Codable {
  var id = UUID()
  let timestamp: Date
  let level: LogLevel
  let message: String
  let file: String
  let function: String
  let line: Int
  let category: String?
}

// MARK: - Logging Extensions

extension LoggingServiceProtocol {
  func logDebug(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    logDebug(message, file: file, function: function, line: line)
  }

  func logInfo(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    logInfo(message, file: file, function: function, line: line)
  }

  func logWarning(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    logWarning(message, file: file, function: function, line: line)
  }

  func logError(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    logError(message, file: file, function: function, line: line)
  }

  func logUserInteraction(
    _ action: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    logUserInteraction(action, file: file, function: function, line: line)
  }

  func logPerformance(
    operation: String, duration: TimeInterval, file: String = #file, function: String = #function,
    line: Int = #line
  ) {
    logPerformance(
      operation: operation, duration: duration, file: file, function: function, line: line)
  }
}
