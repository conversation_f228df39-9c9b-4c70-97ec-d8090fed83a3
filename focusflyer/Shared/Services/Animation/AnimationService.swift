//
//  AnimationService.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import Combine
import Foundation
import RiveRuntime

/// 动画服务实现 - 管理 Rive 动画的播放和控制
@MainActor
final class AnimationService: AnimationServiceProtocol, ObservableObject {

  // MARK: - Published Properties
  @Published private(set) var isAnimationPlaying: Bool = false
  @Published private(set) var currentAnimation: String?

  // MARK: - Private Properties
  private let loggingService: LoggingServiceProtocol
  private var riveViewModel: RiveViewModel?
  private var animationCache: [String: RiveViewModel] = [:]
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Animation Configuration
  private let animationDirectory = "RiveAssets"
  private let defaultAnimationFile = "spegni_il_cervello"

  // MARK: - Initialization

  init(loggingService: LoggingServiceProtocol) {
    self.loggingService = loggingService

    self.loggingService.logInfo("🎬 AnimationService 初始化完成")
    preloadAnimations()
  }

  // MARK: - Public Methods

  func playAnimation(named name: String) async {
    loggingService.logInfo("▶️ 播放动画: \(name)")

    let startTime = Date()

    // 停止当前动画
    if isAnimationPlaying {
      await stopAnimation()
    }

    // 创建或获取RiveViewModel
    let viewModel = getOrCreateViewModel(for: name)
    self.riveViewModel = viewModel

    // 播放动画
    viewModel.play()

    self.currentAnimation = name
    self.isAnimationPlaying = true

    let duration = Date().timeIntervalSince(startTime)
    loggingService.logPerformance(operation: "Animation Play", duration: duration)
    loggingService.logInfo("✅ 动画播放成功: \(name)")
  }

  func stopAnimation() async {
    guard isAnimationPlaying else { return }

    loggingService.logInfo("⏹️ 停止动画")

    riveViewModel?.stop()
    self.isAnimationPlaying = false
    self.currentAnimation = nil
    self.riveViewModel = nil

    loggingService.logInfo("✅ 动画已停止")
  }

  func pauseAnimation() async {
    guard isAnimationPlaying else { return }

    loggingService.logInfo("⏸️ 暂停动画")

    riveViewModel?.pause()
    self.isAnimationPlaying = false

    loggingService.logInfo("✅ 动画已暂停")
  }

  func resumeAnimation() async {
    guard let currentAnimation = currentAnimation, !isAnimationPlaying else { return }

    loggingService.logInfo("▶️ 恢复动画: \(currentAnimation)")

    riveViewModel?.play()
    self.isAnimationPlaying = true

    loggingService.logInfo("✅ 动画恢复播放")
  }

  // MARK: - Animation Loading

  private func getOrCreateViewModel(for name: String) -> RiveViewModel {
    // 首先检查缓存
    if let cachedViewModel = animationCache[name] {
      loggingService.logInfo("📦 从缓存获取动画ViewModel: \(name)")
      return cachedViewModel
    }

    // 创建新的ViewModel
    let viewModel = RiveViewModel(
      fileName: name,
      autoPlay: false  // 不自动播放，由服务控制
    )

    // 缓存ViewModel
    animationCache[name] = viewModel

    loggingService.logInfo("✅ 创建新的动画ViewModel: \(name)")
    return viewModel
  }

  // MARK: - Animation Management

  func getAvailableAnimations() -> [AnimationData] {
    var animations: [AnimationData] = []

    // 简化实现：返回默认动画
    let animationData = AnimationData(
      name: defaultAnimationFile,
      duration: 1.0,
      isLooping: true,
      filePath: "RiveAssets/\(defaultAnimationFile).riv"
    )
    animations.append(animationData)

    return animations
  }

  func preloadAnimations() {
    loggingService.logInfo("📚 预加载动画资源")

    // 预创建默认动画的ViewModel
    let _ = getOrCreateViewModel(for: defaultAnimationFile)

    loggingService.logInfo("📚 动画预加载完成，已缓存 \(animationCache.count) 个动画")
  }

  func clearCache() {
    loggingService.logInfo("🗑️ 清理动画缓存")

    animationCache.removeAll()

    loggingService.logInfo("✅ 动画缓存已清理")
  }

  // MARK: - Animation Control

  func setAnimationSpeed(_ speed: Float) {
    guard riveViewModel != nil else { return }

    loggingService.logInfo("⚡ 设置动画速度: \(speed)")

    // Rive 的速度控制实现
    // 这里需要根据 RiveRuntime 的 API 来实现
    // riveModel.setSpeed(speed)

    loggingService.logInfo("✅ 动画速度已设置")
  }

  func seekToTime(_ time: TimeInterval) {
    guard riveViewModel != nil else { return }

    loggingService.logInfo("⏭️ 跳转到时间: \(time)秒")

    // Rive 的时间跳转实现
    // riveModel.seekTo(time)

    loggingService.logInfo("✅ 动画时间跳转完成")
  }

  func setLoop(_ isLooping: Bool) {
    guard riveViewModel != nil else { return }

    loggingService.logInfo("🔄 设置循环播放: \(isLooping)")

    // Rive 的循环设置实现
    // riveModel.setLoop(isLooping)

    loggingService.logInfo("✅ 循环设置已更新")
  }
}

// MARK: - Animation Statistics

extension AnimationService {

  struct AnimationStatistics {
    let totalAnimations: Int
    let cachedAnimations: Int
    let currentAnimation: String?
    let isPlaying: Bool
    let cacheMemoryUsage: UInt64
  }

  func getStatistics() -> AnimationStatistics {
    return AnimationStatistics(
      totalAnimations: getAvailableAnimations().count,
      cachedAnimations: animationCache.count,
      currentAnimation: currentAnimation,
      isPlaying: isAnimationPlaying,
      cacheMemoryUsage: estimateCacheMemoryUsage()
    )
  }

  private func estimateCacheMemoryUsage() -> UInt64 {
    // 估算缓存内存使用量
    // 这是一个简化的实现
    return UInt64(animationCache.count * 1024 * 1024)  // 每个动画估计 1MB
  }
}

// MARK: - Error Handling

extension AnimationService {

  enum AnimationError: LocalizedError {
    case fileNotFound(String)
    case loadFailed(String, String)
    case playFailed(String)
    case notSupported(String)

    var errorDescription: String? {
      switch self {
      case .fileNotFound(let name):
        return "动画文件未找到: \(name)"
      case .loadFailed(let name, let reason):
        return "动画加载失败: \(name) - \(reason)"
      case .playFailed(let name):
        return "动画播放失败: \(name)"
      case .notSupported(let format):
        return "不支持的动画格式: \(format)"
      }
    }
  }

  func handleError(_ error: AnimationError) {
    loggingService.logError("动画错误: \(error.localizedDescription)")

    // 停止当前动画
    Task {
      await stopAnimation()
    }
  }
}

// MARK: - Animation Events

extension AnimationService {

  /// 动画事件监听
  func setupAnimationObservers() {
    // 这里可以设置 Rive 动画的事件监听
    // 例如动画完成、循环、状态机切换等事件

    loggingService.logInfo("🔔 设置动画事件监听")
  }

  /// 处理动画完成事件
  private func onAnimationCompleted(_ animationName: String) {
    loggingService.logInfo("🏁 动画播放完成: \(animationName)")

    DispatchQueue.main.async { [weak self] in
      self?.isAnimationPlaying = false
      if self?.currentAnimation == animationName {
        self?.currentAnimation = nil
      }
    }
  }

  /// 处理动画错误事件
  private func onAnimationError(_ animationName: String, error: Error) {
    loggingService.logError("动画运行时错误: \(animationName) - \(error)")

    DispatchQueue.main.async { [weak self] in
      self?.isAnimationPlaying = false
    }
  }
}
