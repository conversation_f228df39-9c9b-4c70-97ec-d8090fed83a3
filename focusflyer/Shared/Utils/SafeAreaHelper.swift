//  SafeAreaHelper.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import SwiftUI
import UIKit

/// 安全边界工具类 - 统一处理各种设备的安全区域
public struct SafeAreaHelper {

  // MARK: - 设备类型检测

  /// 设备类型
  public enum DeviceType {
    case iPhoneWithNotch  // iPhone X 系列（刘海屏）
    case iPhoneWithDynamicIsland  // iPhone 14 Pro 系列（灵动岛）
    case iPhoneClassic  // 传统 iPhone（Home 键）
    case iPadModern  // 现代 iPad（全面屏）
    case iPadClassic  // 传统 iPad（Home 键）
    case unknown
  }

  /// 获取当前设备类型
  public static var deviceType: DeviceType {
    let device = UIDevice.current
    let screen = UIScreen.main

    // 基于屏幕尺寸和安全区域判断设备类型
    if device.userInterfaceIdiom == .phone {
      let screenHeight = max(screen.bounds.width, screen.bounds.height)
      let safeAreaTop = UIApplication.shared.windows.first?.safeAreaInsets.top ?? 0

      if safeAreaTop > 50 {
        // 灵动岛设备
        return .iPhoneWithDynamicIsland
      } else if safeAreaTop > 20 {
        // 刘海屏设备
        return .iPhoneWithNotch
      } else {
        // 传统设备
        return .iPhoneClassic
      }
    } else if device.userInterfaceIdiom == .pad {
      let safeAreaTop = UIApplication.shared.windows.first?.safeAreaInsets.top ?? 0
      return safeAreaTop > 20 ? .iPadModern : .iPadClassic
    }

    return .unknown
  }

  // MARK: - 安全区域计算

  /// 获取当前窗口的安全区域
  public static var currentSafeAreaInsets: EdgeInsets {
    guard let window = UIApplication.shared.windows.first else {
      return EdgeInsets()
    }

    let insets = window.safeAreaInsets
    return EdgeInsets(
      top: insets.top,
      leading: insets.left,
      bottom: insets.bottom,
      trailing: insets.right
    )
  }

  /// 基于 GeometryReader 获取安全区域
  public static func safeAreaInsets(from geometry: GeometryProxy) -> EdgeInsets {
    let insets = geometry.safeAreaInsets
    return EdgeInsets(
      top: insets.top,
      leading: insets.leading,
      bottom: insets.bottom,
      trailing: insets.trailing
    )
  }

  // MARK: - 智能位置计算

  /// 计算悬浮元素的安全位置
  /// - Parameters:
  ///   - position: 期望位置
  ///   - elementSize: 元素尺寸
  ///   - geometry: 几何信息
  ///   - margin: 距离边缘的最小间距
  /// - Returns: 调整后的安全位置
  public static func calculateSafePosition(
    for position: CGPoint,
    elementSize: CGSize,
    in geometry: GeometryProxy,
    margin: CGFloat = 16
  ) -> CGPoint {
    let screenSize = geometry.size
    let safeArea = geometry.safeAreaInsets

    // 计算可用区域
    let availableRect = CGRect(
      x: safeArea.leading + margin,
      y: safeArea.top + margin,
      width: screenSize.width - safeArea.leading - safeArea.trailing - margin * 2,
      height: screenSize.height - safeArea.top - safeArea.bottom - margin * 2
    )

    // 调整位置确保元素完全在可用区域内
    let halfWidth = elementSize.width / 2
    let halfHeight = elementSize.height / 2

    let safeX = max(
      availableRect.minX + halfWidth,
      min(availableRect.maxX - halfWidth, position.x))
    let safeY = max(
      availableRect.minY + halfHeight,
      min(availableRect.maxY - halfHeight, position.y))

    return CGPoint(x: safeX, y: safeY)
  }

  /// 计算展开面板的最大尺寸
  /// - Parameters:
  ///   - anchorPosition: 锚点位置
  ///   - geometry: 几何信息
  ///   - preferredDirection: 首选展开方向
  /// - Returns: 最大可用尺寸
  public static func calculateMaxPanelSize(
    anchorPosition: CGPoint,
    in geometry: GeometryProxy,
    preferredDirection: PanelDirection = .auto
  ) -> CGSize {
    let screenSize = geometry.size
    let safeArea = geometry.safeAreaInsets
    let margin: CGFloat = 16

    // 计算各方向可用空间
    let leftSpace = anchorPosition.x - safeArea.leading - margin
    let rightSpace = screenSize.width - anchorPosition.x - safeArea.trailing - margin
    let topSpace = anchorPosition.y - safeArea.top - margin
    let bottomSpace = screenSize.height - anchorPosition.y - safeArea.bottom - margin

    // 根据首选方向或自动选择
    let direction =
      preferredDirection == .auto
      ? (anchorPosition.x < screenSize.width / 2 ? PanelDirection.right : PanelDirection.left)
      : preferredDirection

    let maxWidth: CGFloat
    let maxHeight: CGFloat

    switch direction {
    case .left:
      maxWidth = leftSpace * 2
    case .right:
      maxWidth = rightSpace * 2
    case .up:
      maxWidth = min(leftSpace, rightSpace) * 2
    case .down:
      maxWidth = min(leftSpace, rightSpace) * 2
    case .auto:
      maxWidth = min(leftSpace, rightSpace) * 2
    }

    maxHeight = min(topSpace, bottomSpace) * 2

    // 确保最小尺寸
    return CGSize(
      width: max(200, min(maxWidth, screenSize.width * 0.8)),
      height: max(150, min(maxHeight, screenSize.height * 0.6))
    )
  }

  // MARK: - 磁吸功能

  /// 磁吸位置计算
  /// - Parameters:
  ///   - position: 当前位置
  ///   - geometry: 几何信息
  ///   - magnetDistance: 磁吸距离
  /// - Returns: 磁吸后的位置
  public static func magneticSnap(
    position: CGPoint,
    in geometry: GeometryProxy,
    magnetDistance: CGFloat = 80
  ) -> CGPoint {
    let screenSize = geometry.size
    let safeArea = geometry.safeAreaInsets

    // 左右磁吸
    let snapX: CGFloat
    if position.x < screenSize.width / 2 {
      snapX = magnetDistance + safeArea.leading
    } else {
      snapX = screenSize.width - magnetDistance - safeArea.trailing
    }

    // 上下限制在安全区域内
    let minY = magnetDistance + safeArea.top
    let maxY = screenSize.height - magnetDistance - safeArea.bottom
    let snapY = max(minY, min(maxY, position.y))

    return CGPoint(x: snapX, y: snapY)
  }

  // MARK: - 面板展开计算

  /// 计算展开面板的最大宽度
  /// - Parameters:
  ///   - anchorPosition: 锚点位置（按钮位置）
  ///   - geometry: 几何信息
  ///   - margin: 边距
  /// - Returns: 最大可用宽度
  public static func calculateMaxPanelWidth(
    anchorPosition: CGPoint,
    in geometry: GeometryProxy,
    margin: CGFloat = 16
  ) -> CGFloat {
    let screenWidth = geometry.size.width
    let safeArea = geometry.safeAreaInsets
    
    // 根据按钮位置计算可用空间
    let rightSpace = screenWidth - anchorPosition.x - safeArea.trailing - margin
    let leftSpace = anchorPosition.x - safeArea.leading - margin
    
    // 优先使用更大的空间，但限制最大宽度
    let availableSpace = max(rightSpace, leftSpace)
    return max(180, min(availableSpace, screenWidth * 0.75))
  }
  
  /// 计算展开面板的水平偏移量
  /// - Parameters:
  ///   - anchorPosition: 锚点位置（按钮位置）
  ///   - panelWidth: 面板宽度
  ///   - geometry: 几何信息
  ///   - margin: 边距
  /// - Returns: 水平偏移量
  public static func calculatePanelOffset(
    anchorPosition: CGPoint,
    panelWidth: CGFloat,
    in geometry: GeometryProxy,
    margin: CGFloat = 16
  ) -> CGFloat {
    let screenWidth = geometry.size.width
    let safeArea = geometry.safeAreaInsets
    
    // 检查面板是否会超出右边界
    let panelRightEdge = anchorPosition.x + panelWidth/2
    let screenRightEdge = screenWidth - safeArea.trailing - margin
    
    if panelRightEdge > screenRightEdge {
      // 如果超出右边界，向左偏移
      return screenRightEdge - panelRightEdge
    }
    
    // 检查面板是否会超出左边界
    let panelLeftEdge = anchorPosition.x - panelWidth/2
    let screenLeftEdge = safeArea.leading + margin
    
    if panelLeftEdge < screenLeftEdge {
      // 如果超出左边界，向右偏移
      return screenLeftEdge - panelLeftEdge
    }
    
    return 0  // 不需要偏移
  }

  /// 计算面板展开时的最佳锚点位置（如果当前位置会导致面板超出边界，则调整锚点位置）
  /// - Parameters:
  ///   - currentPosition: 当前锚点位置
  ///   - panelSize: 面板尺寸
  ///   - geometry: 几何信息
  ///   - margin: 边距
  /// - Returns: 调整后的锚点位置
  public static func calculateOptimalAnchorPosition(
    currentPosition: CGPoint,
    panelSize: CGSize,
    in geometry: GeometryProxy,
    margin: CGFloat = 16
  ) -> CGPoint {
    let screenSize = geometry.size
    let safeArea = geometry.safeAreaInsets
    
    // 计算面板边界
    let panelLeftEdge = currentPosition.x - panelSize.width/2
    let panelRightEdge = currentPosition.x + panelSize.width/2
    let panelTopEdge = currentPosition.y - panelSize.height/2
    let panelBottomEdge = currentPosition.y + panelSize.height/2
    
    // 计算安全区域边界
    let safeLeft = safeArea.leading + margin
    let safeRight = screenSize.width - safeArea.trailing - margin
    let safeTop = safeArea.top + margin  
    let safeBottom = screenSize.height - safeArea.bottom - margin
    
    var adjustedPosition = currentPosition
    
    // 水平调整
    if panelLeftEdge < safeLeft {
      adjustedPosition.x = safeLeft + panelSize.width/2
    } else if panelRightEdge > safeRight {
      adjustedPosition.x = safeRight - panelSize.width/2
    }
    
    // 垂直调整
    if panelTopEdge < safeTop {
      adjustedPosition.y = safeTop + panelSize.height/2
    } else if panelBottomEdge > safeBottom {
      adjustedPosition.y = safeBottom - panelSize.height/2
    }
    
    return adjustedPosition
  }

  /// 计算状态面板的安全顶部位置
  /// - Parameters:
  ///   - geometry: 几何信息
  ///   - panelHeight: 面板高度
  /// - Returns: 安全的顶部位置
  public static func calculateStatusPanelSafeTopPosition(
    in geometry: GeometryProxy,
    panelHeight: CGFloat
  ) -> CGFloat {
    let safeArea = geometry.safeAreaInsets
    let deviceMargins = deviceSpecificMargins
    
    // 计算安全顶部位置
    let baseTopPosition = safeArea.top + deviceMargins.top
    
    // 根据设备类型添加额外边距
    let extraMargin: CGFloat
    switch deviceType {
    case .iPhoneWithDynamicIsland:
      extraMargin = 15 // 灵动岛需要更多空间
    case .iPhoneWithNotch:
      extraMargin = 10 // 刘海屏需要适中空间
    default:
      extraMargin = 5  // 其他设备较少空间
    }
    
    return baseTopPosition + extraMargin
  }

  // MARK: - 适配不同设备

  /// 获取设备特定的安全边距
  public static var deviceSpecificMargins: EdgeInsets {
    switch deviceType {
    case .iPhoneWithDynamicIsland:
      return EdgeInsets(top: 35, leading: 16, bottom: 40, trailing: 16) // 增加灵动岛顶部和底部边距
    case .iPhoneWithNotch:
      return EdgeInsets(top: 25, leading: 16, bottom: 40, trailing: 16) // 增加刘海屏顶部和底部边距
    case .iPhoneClassic:
      return EdgeInsets(top: 20, leading: 16, bottom: 20, trailing: 16)
    case .iPadModern:
      return EdgeInsets(top: 30, leading: 20, bottom: 25, trailing: 20)
    case .iPadClassic:
      return EdgeInsets(top: 25, leading: 20, bottom: 20, trailing: 20)
    case .unknown:
      return EdgeInsets(top: 25, leading: 16, bottom: 25, trailing: 16)
    }
  }

  /// 获取设备特定的最小触摸目标尺寸
  public static var minTouchTargetSize: CGFloat {
    switch deviceType {
    case .iPadModern, .iPadClassic:
      return 50  // iPad 更大的触摸目标
    default:
      return 44  // iPhone 标准触摸目标
    }
  }
}

// MARK: - 面板展开方向

public enum PanelDirection {
  case left
  case right
  case up
  case down
  case auto
}

// MARK: - SwiftUI 扩展

extension View {
  /// 应用安全边界适配
  public func safeAreaAdaptive(geometry: GeometryProxy) -> some View {
    let safeArea = SafeAreaHelper.safeAreaInsets(from: geometry)
    return self.padding(safeArea)
  }

  /// 应用设备特定边距
  public func deviceSpecificPadding() -> some View {
    self.padding(SafeAreaHelper.deviceSpecificMargins)
  }

  /// 确保最小触摸目标尺寸
  public func minTouchTarget() -> some View {
    let minSize = SafeAreaHelper.minTouchTargetSize
    return self.frame(minWidth: minSize, minHeight: minSize)
  }
}

// MARK: - 调试工具

#if DEBUG
  extension SafeAreaHelper {
    /// 调试信息
    public static var debugInfo: String {
      let device = deviceType
      let safeArea = currentSafeAreaInsets
      let margins = deviceSpecificMargins

      return """
        📱 设备类型: \(device)
        🔒 安全区域: top=\(safeArea.top), bottom=\(safeArea.bottom), leading=\(safeArea.leading), trailing=\(safeArea.trailing)
        📏 设备边距: top=\(margins.top), bottom=\(margins.bottom), leading=\(margins.leading), trailing=\(margins.trailing)
        👆 最小触摸: \(minTouchTargetSize)pt
        """
    }
  }

  /// 安全区域可视化调试视图
  public struct SafeAreaDebugOverlay: View {
    let geometry: GeometryProxy

    public var body: some View {
      let safeArea = geometry.safeAreaInsets

      ZStack {
        // 安全区域边界
        Rectangle()
          .stroke(Color.red.opacity(0.5), lineWidth: 2)
          .frame(
            width: geometry.size.width - safeArea.leading - safeArea.trailing,
            height: geometry.size.height - safeArea.top - safeArea.bottom
          )
          .position(
            x: geometry.size.width / 2,
            y: geometry.size.height / 2
          )

        // 角落标记
        VStack {
          HStack {
            Circle()
              .fill(Color.red)
              .frame(width: 8, height: 8)
            Spacer()
            Circle()
              .fill(Color.red)
              .frame(width: 8, height: 8)
          }
          Spacer()
          HStack {
            Circle()
              .fill(Color.red)
              .frame(width: 8, height: 8)
            Spacer()
            Circle()
              .fill(Color.red)
              .frame(width: 8, height: 8)
          }
        }
        .padding(safeArea)
      }
      .allowsHitTesting(false)
    }
  }

  extension View {
    /// 添加安全区域调试覆盖层
    public func debugSafeArea() -> some View {
      GeometryReader { geometry in
        ZStack {
          self
          SafeAreaDebugOverlay(geometry: geometry)
        }
      }
    }
  }
#endif
