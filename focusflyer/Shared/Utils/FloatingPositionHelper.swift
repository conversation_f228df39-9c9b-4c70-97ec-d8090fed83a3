//
//  FloatingPositionHelper.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import SwiftUI

/// 悬浮位置计算工具类 - 统一处理悬浮元素的位置逻辑
public struct FloatingPositionHelper {
    
    // MARK: - 悬浮元素类型
    
    /// 悬浮元素类型
    public enum FloatingType {
        case button(size: CGFloat)           // 悬浮按钮
        case panel(size: CGSize)            // 悬浮面板
        case expandedPanel(estimatedSize: CGSize)  // 展开的面板
    }
    
    /// 悬浮位置偏好
    public enum PositionPreference {
        case topLeft
        case topRight
        case bottomLeft
        case bottomRight
        case auto  // 自动选择最佳位置
    }
    
    // MARK: - 初始位置计算
    
    /// 计算悬浮按钮的初始位置
    /// - Parameters:
    ///   - preference: 位置偏好
    ///   - buttonSize: 按钮大小
    ///   - geometry: 几何信息
    ///   - margin: 距离边缘的边距
    /// - Returns: 初始位置
    public static func calculateInitialPosition(
        preference: PositionPreference = .bottomRight,
        buttonSize: CGFloat = 60,
        in geometry: GeometryProxy,
        margin: CGFloat = 80
    ) -> CGPoint {
        let screenSize = geometry.size
        let safeArea = geometry.safeAreaInsets
        
        let safeLeft = safeArea.leading + margin
        let safeRight = screenSize.width - safeArea.trailing - margin
        let safeTop = safeArea.top + margin
        let safeBottom = screenSize.height - safeArea.bottom - margin
        
        switch preference {
        case .topLeft:
            return CGPoint(x: safeLeft, y: safeTop)
        case .topRight:
            return CGPoint(x: safeRight, y: safeTop)
        case .bottomLeft:
            return CGPoint(x: safeLeft, y: safeBottom)
        case .bottomRight:
            return CGPoint(x: safeRight, y: safeBottom)
        case .auto:
            // 默认右下角
            return CGPoint(x: safeRight, y: safeBottom)
        }
    }
    
    // MARK: - 拖拽跟手逻辑
    
    /// 拖拽状态
    public struct DragState {
        public var startPosition: CGPoint = .zero
        public var isDragging: Bool = false
        public var hasActuallyDragged: Bool = false
        private var dragStartTime: Date = Date()
        
        public init() {}
        
        public mutating func beginDrag(at position: CGPoint) {
            startPosition = position
            isDragging = true
            hasActuallyDragged = false
            dragStartTime = Date()
        }
        
        public mutating func updateDrag(translation: CGSize) {
            // 如果拖拽距离超过阈值，标记为实际拖拽
            let dragDistance = sqrt(translation.width * translation.width + translation.height * translation.height)
            if dragDistance > 15 { // 增加拖拽阈值
                hasActuallyDragged = true
            }
        }
        
        public mutating func endDrag() {
            startPosition = .zero
            isDragging = false
            // hasActuallyDragged 将在 FloatingPanelManager 中延迟重置
        }
        
        public var shouldPreventClick: Bool {
            return isDragging || hasActuallyDragged
        }
    }
    
    /// 计算拖拽过程中的位置
    /// - Parameters:
    ///   - dragState: 拖拽状态
    ///   - translation: 拖拽偏移量
    ///   - geometry: 几何信息
    ///   - elementSize: 元素大小
    ///   - margin: 边距
    /// - Returns: 新位置
    public static func calculateDragPosition(
        dragState: DragState,
        translation: CGSize,
        in geometry: GeometryProxy,
        elementSize: CGFloat = 60,
        margin: CGFloat = 30
    ) -> CGPoint {
        let newX = dragState.startPosition.x + translation.width
        let newY = dragState.startPosition.y + translation.height
        
        // 使用 SafeAreaHelper 的边界限制
        let safePosition = SafeAreaHelper.calculateSafePosition(
            for: CGPoint(x: newX, y: newY),
            elementSize: CGSize(width: elementSize, height: elementSize),
            in: geometry,
            margin: margin
        )
        
        return safePosition
    }
    
    // MARK: - 控制面板位置计算
    
    /// 计算展开面板的位置和大小
    /// - Parameters:
    ///   - anchorPosition: 锚点位置（按钮位置）
    ///   - panelSize: 面板大小
    ///   - geometry: 几何信息
    /// - Returns: (面板位置, 实际大小, 水平偏移量)
    public static func calculatePanelLayout(
        anchorPosition: CGPoint,
        estimatedPanelSize: CGSize,
        in geometry: GeometryProxy
    ) -> (position: CGPoint, size: CGSize, offset: CGPoint) {
        
        // 计算最大可用尺寸
        let maxWidth = SafeAreaHelper.calculateMaxPanelWidth(
            anchorPosition: anchorPosition,
            in: geometry
        )
        
        // 调整面板尺寸
        let actualSize = CGSize(
            width: min(estimatedPanelSize.width, maxWidth),
            height: estimatedPanelSize.height
        )
        
        // 计算水平偏移
        let horizontalOffset = SafeAreaHelper.calculatePanelOffset(
            anchorPosition: anchorPosition,
            panelWidth: actualSize.width,
            in: geometry
        )
        
        // 计算最终位置
        let finalPosition = CGPoint(
            x: anchorPosition.x + horizontalOffset,
            y: anchorPosition.y
        )
        
        return (
            position: finalPosition,
            size: actualSize,
            offset: CGPoint(x: horizontalOffset, y: 0)
        )
    }
    
    // MARK: - 智能吸附逻辑
    
    /// 计算磁吸位置（针对按钮状态）
    /// - Parameters:
    ///   - currentPosition: 当前位置
    ///   - geometry: 几何信息
    ///   - magnetDistance: 磁吸距离
    /// - Returns: 磁吸后的位置
    public static func calculateMagneticSnapPosition(
        currentPosition: CGPoint,
        in geometry: GeometryProxy,
        magnetDistance: CGFloat = 80
    ) -> CGPoint {
        return SafeAreaHelper.magneticSnap(
            position: currentPosition,
            in: geometry,
            magnetDistance: magnetDistance
        )
    }
    
    /// 计算展开面板的安全位置（基于面板四角）
    /// - Parameters:
    ///   - anchorPosition: 锚点位置
    ///   - panelSize: 面板尺寸
    ///   - geometry: 几何信息
    /// - Returns: 调整后的安全锚点位置
    public static func calculateExpandedPanelSafePosition(
        anchorPosition: CGPoint,
        panelSize: CGSize,
        in geometry: GeometryProxy
    ) -> CGPoint {
        let screenSize = geometry.size
        let safeArea = geometry.safeAreaInsets
        
        // 根据设备类型调整边距
        let deviceMargins = SafeAreaHelper.deviceSpecificMargins
        let topMargin = max(safeArea.top + deviceMargins.top, 60) // 确保顶部有足够空间
        let bottomMargin = max(safeArea.bottom + deviceMargins.bottom, 40) // 确保底部有足够空间
        let sideMargin = deviceMargins.leading + 8 // 左右边距
        
        // 计算面板的四个角相对于锚点的位置
        let halfWidth = panelSize.width / 2
        let halfHeight = panelSize.height / 2
        
        // 计算严格的安全区域边界
        let safeLeft = safeArea.leading + sideMargin
        let safeRight = screenSize.width - safeArea.trailing - sideMargin
        let safeTop = topMargin
        let safeBottom = screenSize.height - bottomMargin
        
        var adjustedPosition = anchorPosition
        
        // 检查并调整水平位置（确保面板左右边缘不超出）
        let panelLeft = anchorPosition.x - halfWidth
        let panelRight = anchorPosition.x + halfWidth
        
        if panelLeft < safeLeft {
            adjustedPosition.x = safeLeft + halfWidth
        } else if panelRight > safeRight {
            adjustedPosition.x = safeRight - halfWidth
        }
        
        // 检查并调整垂直位置（确保面板上下边缘不超出）
        let panelTop = anchorPosition.y - halfHeight
        let panelBottom = anchorPosition.y + halfHeight
        
        if panelTop < safeTop {
            adjustedPosition.y = safeTop + halfHeight
        } else if panelBottom > safeBottom {
            adjustedPosition.y = safeBottom - halfHeight
        }
        
        // 智能吸附逻辑 - 根据位置决定吸附方向
        let snapThreshold: CGFloat = 50
        let centerX = screenSize.width / 2
        let centerY = screenSize.height / 2
        
        // 水平吸附 - 优先吸附到更近的边
        if adjustedPosition.x < centerX {
            // 靠近左边，检查是否需要左吸附
            if abs(adjustedPosition.x - (safeLeft + halfWidth)) < snapThreshold {
                adjustedPosition.x = safeLeft + halfWidth
            }
        } else {
            // 靠近右边，检查是否需要右吸附
            if abs(adjustedPosition.x - (safeRight - halfWidth)) < snapThreshold {
                adjustedPosition.x = safeRight - halfWidth
            }
        }
        
        // 垂直吸附 - 防止过于接近顶部或底部
        if adjustedPosition.y < centerY {
            // 上半部分，确保不要太靠近顶部
            let minTopY = safeTop + halfHeight + 20
            if adjustedPosition.y < minTopY {
                adjustedPosition.y = minTopY
            }
        } else {
            // 下半部分，确保不要太靠近底部
            let maxBottomY = safeBottom - halfHeight - 20
            if adjustedPosition.y > maxBottomY {
                adjustedPosition.y = maxBottomY
            }
        }
        
        return adjustedPosition
    }
    
    /// 检查面板是否需要位置调整
    /// - Parameters:
    ///   - anchorPosition: 锚点位置
    ///   - panelSize: 面板大小
    ///   - geometry: 几何信息
    /// - Returns: 调整后的锚点位置（如果需要调整）
    public static func calculateAdjustedAnchorPosition(
        anchorPosition: CGPoint,
        panelSize: CGSize,
        in geometry: GeometryProxy
    ) -> CGPoint {
        return SafeAreaHelper.calculateOptimalAnchorPosition(
            currentPosition: anchorPosition,
            panelSize: panelSize,
            in: geometry
        )
    }
    
    // MARK: - 动画配置
    
    /// 位置调整动画配置
    public static let positionAdjustmentAnimation = Animation.spring(
        response: 0.4,
        dampingFraction: 0.8
    )
    
    /// 磁吸动画配置
    public static let magneticSnapAnimation = Animation.spring(
        response: 0.6,
        dampingFraction: 0.7
    )
    
    /// 展开/收起动画配置
    public static let expandAnimation = Animation.spring(
        response: 0.5,
        dampingFraction: 0.7
    )
}

// MARK: - 悬浮面板管理器

/// 悬浮面板状态管理器
public class FloatingPanelManager: ObservableObject {
    @Published public var position: CGPoint = .zero
    @Published public var isExpanded: Bool = false
    @Published public var dragState = FloatingPositionHelper.DragState()
    public var geometry: GeometryProxy?
    
    public init() {}
    
    // MARK: - 位置管理
    
    /// 设置初始位置
    public func setInitialPosition(
        preference: FloatingPositionHelper.PositionPreference = .bottomRight,
        buttonSize: CGFloat = 60,
        geometry: GeometryProxy
    ) {
        self.geometry = geometry
        self.position = FloatingPositionHelper.calculateInitialPosition(
            preference: preference,
            buttonSize: buttonSize,
            in: geometry
        )
    }
    
    /// 开始拖拽
    public func beginDrag() {
        dragState.beginDrag(at: position)
    }
    
    /// 更新拖拽位置
    public func updateDragPosition(translation: CGSize, elementSize: CGFloat = 60) {
        guard let geometry = geometry else { return }
        
        // 更新拖拽状态以检测是否为实际拖拽
        dragState.updateDrag(translation: translation)
        
        position = FloatingPositionHelper.calculateDragPosition(
            dragState: dragState,
            translation: translation,
            in: geometry,
            elementSize: elementSize
        )
    }
    
    /// 结束拖拽并磁吸
    public func endDragWithSnap(estimatedPanelSize: CGSize? = nil) {
        guard let geometry = geometry else { return }
        
        if isExpanded, let panelSize = estimatedPanelSize {
            // 展开状态：基于面板四角进行边界检测和吸附
            position = FloatingPositionHelper.calculateExpandedPanelSafePosition(
                anchorPosition: position,
                panelSize: panelSize,
                in: geometry
            )
        } else {
            // 收起状态：基于按钮位置进行磁吸
            position = FloatingPositionHelper.calculateMagneticSnapPosition(
                currentPosition: position,
                in: geometry
            )
        }
        
        dragState.endDrag()
        
        // 延迟重置 hasActuallyDragged 以防止立即触发点击
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.dragState.hasActuallyDragged = false
        }
    }
    
    // MARK: - 面板展开管理
    
    /// 切换展开状态
    public func toggleExpanded(estimatedPanelSize: CGSize) {
        guard let geometry = geometry else { return }
        
        isExpanded.toggle()
        
        if isExpanded {
            // 展开时，使用更严格的安全位置计算
            let adjustedPosition = FloatingPositionHelper.calculateExpandedPanelSafePosition(
                anchorPosition: position,
                panelSize: estimatedPanelSize,
                in: geometry
            )
            
            // 总是调整位置以确保展开面板完全在安全区域内
            if adjustedPosition != position {
                withAnimation(FloatingPositionHelper.positionAdjustmentAnimation) {
                    position = adjustedPosition
                }
            }
        } else {
            // 收起时，检查是否需要磁吸
            let snapPosition = FloatingPositionHelper.calculateMagneticSnapPosition(
                currentPosition: position,
                in: geometry,
                magnetDistance: 80
            )
            
            if snapPosition != position {
                withAnimation(FloatingPositionHelper.magneticSnapAnimation) {
                    position = snapPosition
                }
            }
        }
    }
    
    /// 获取面板布局信息
    public func getPanelLayout(estimatedSize: CGSize) -> (position: CGPoint, size: CGSize, offset: CGPoint)? {
        guard let geometry = geometry else { return nil }
        
        return FloatingPositionHelper.calculatePanelLayout(
            anchorPosition: position,
            estimatedPanelSize: estimatedSize,
            in: geometry
        )
    }
}

// MARK: - SwiftUI 扩展

extension View {
    /// 应用悬浮拖拽手势
    public func floatingDragGesture(
        manager: FloatingPanelManager,
        elementSize: CGFloat = 60,
        minimumDistance: CGFloat = 10,
        estimatedPanelSize: CGSize? = nil
    ) -> some View {
        self.simultaneousGesture(
            DragGesture(minimumDistance: minimumDistance)
                .onChanged { value in
                    if manager.dragState.startPosition == .zero {
                        manager.beginDrag()
                    }
                    manager.updateDragPosition(
                        translation: value.translation,
                        elementSize: elementSize
                    )
                }
                .onEnded { _ in
                    withAnimation(FloatingPositionHelper.magneticSnapAnimation) {
                        manager.endDragWithSnap(estimatedPanelSize: estimatedPanelSize)
                    }
                }
        )
    }
}