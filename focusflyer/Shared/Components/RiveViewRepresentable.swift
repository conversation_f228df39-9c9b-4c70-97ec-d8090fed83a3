//
//  RiveViewRepresentable.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/12.
//

import RiveRuntime
import SwiftUI
import UIKit

/// Rive动画的SwiftUI包装器
struct RiveViewRepresentable: UIViewRepresentable {
  let fileName: String
  let animationName: String?
  let fit: RiveFit
  let alignment: RiveAlignment

  // 创建RiveViewModel实例
  private let viewModel: RiveViewModel

  init(
    fileName: String,
    animationName: String? = nil,
    fit: RiveFit = .cover,
    alignment: RiveAlignment = .center
  ) {
    self.fileName = fileName
    self.animationName = animationName
    self.fit = fit
    self.alignment = alignment

    // 创建RiveViewModel
    self.viewModel = RiveViewModel(
      fileName: fileName,
      animationName: animationName,
      autoPlay: true
    )
  }

  func makeUIView(context: Context) -> UIView {
    // 使用RiveViewModel的view()方法创建SwiftUI视图
    let riveSwiftUIView = viewModel.view()

    // 将SwiftUI视图包装在UIHostingController中
    let hostingController = UIHostingController(rootView: riveSwiftUIView)
    hostingController.view.backgroundColor = UIColor.clear

    print("✅ Rive动画视图创建成功: \(fileName)")
    return hostingController.view
  }

  func updateUIView(_ uiView: UIView, context: Context) {
    // 这里可以处理动画的动态更新
    // 例如切换动画、暂停/恢复等
  }
}
